"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ai-sdk";
exports.ids = ["vendor-chunks/@ai-sdk"];
exports.modules = {

/***/ "(ssr)/./node_modules/@ai-sdk/provider-utils/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@ai-sdk/provider-utils/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   asValidator: () => (/* binding */ asValidator),\n/* harmony export */   combineHeaders: () => (/* binding */ combineHeaders),\n/* harmony export */   convertAsyncIteratorToReadableStream: () => (/* binding */ convertAsyncIteratorToReadableStream),\n/* harmony export */   convertBase64ToUint8Array: () => (/* binding */ convertBase64ToUint8Array),\n/* harmony export */   convertUint8ArrayToBase64: () => (/* binding */ convertUint8ArrayToBase64),\n/* harmony export */   createBinaryResponseHandler: () => (/* binding */ createBinaryResponseHandler),\n/* harmony export */   createEventSourceParserStream: () => (/* binding */ createEventSourceParserStream),\n/* harmony export */   createEventSourceResponseHandler: () => (/* binding */ createEventSourceResponseHandler),\n/* harmony export */   createIdGenerator: () => (/* binding */ createIdGenerator),\n/* harmony export */   createJsonErrorResponseHandler: () => (/* binding */ createJsonErrorResponseHandler),\n/* harmony export */   createJsonResponseHandler: () => (/* binding */ createJsonResponseHandler),\n/* harmony export */   createJsonStreamResponseHandler: () => (/* binding */ createJsonStreamResponseHandler),\n/* harmony export */   createStatusCodeErrorResponseHandler: () => (/* binding */ createStatusCodeErrorResponseHandler),\n/* harmony export */   delay: () => (/* binding */ delay),\n/* harmony export */   extractResponseHeaders: () => (/* binding */ extractResponseHeaders),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage),\n/* harmony export */   getFromApi: () => (/* binding */ getFromApi),\n/* harmony export */   isAbortError: () => (/* binding */ isAbortError),\n/* harmony export */   isParsableJson: () => (/* binding */ isParsableJson),\n/* harmony export */   isValidator: () => (/* binding */ isValidator),\n/* harmony export */   loadApiKey: () => (/* binding */ loadApiKey),\n/* harmony export */   loadOptionalSetting: () => (/* binding */ loadOptionalSetting),\n/* harmony export */   loadSetting: () => (/* binding */ loadSetting),\n/* harmony export */   parseJSON: () => (/* binding */ parseJSON),\n/* harmony export */   parseProviderOptions: () => (/* binding */ parseProviderOptions),\n/* harmony export */   postFormDataToApi: () => (/* binding */ postFormDataToApi),\n/* harmony export */   postJsonToApi: () => (/* binding */ postJsonToApi),\n/* harmony export */   postToApi: () => (/* binding */ postToApi),\n/* harmony export */   removeUndefinedEntries: () => (/* binding */ removeUndefinedEntries),\n/* harmony export */   resolve: () => (/* binding */ resolve),\n/* harmony export */   safeParseJSON: () => (/* binding */ safeParseJSON),\n/* harmony export */   safeValidateTypes: () => (/* binding */ safeValidateTypes),\n/* harmony export */   validateTypes: () => (/* binding */ validateTypes),\n/* harmony export */   validator: () => (/* binding */ validator),\n/* harmony export */   validatorSymbol: () => (/* binding */ validatorSymbol),\n/* harmony export */   withoutTrailingSlash: () => (/* binding */ withoutTrailingSlash),\n/* harmony export */   zodValidator: () => (/* binding */ zodValidator)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ai-sdk/provider */ \"(ssr)/./node_modules/@ai-sdk/provider/dist/index.mjs\");\n/* harmony import */ var nanoid_non_secure__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! nanoid/non-secure */ \"(ssr)/./node_modules/nanoid/non-secure/index.js\");\n/* harmony import */ var secure_json_parse__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! secure-json-parse */ \"(ssr)/./node_modules/secure-json-parse/index.js\");\n// src/combine-headers.ts\nfunction combineHeaders(...headers) {\n  return headers.reduce(\n    (combinedHeaders, currentHeaders) => ({\n      ...combinedHeaders,\n      ...currentHeaders != null ? currentHeaders : {}\n    }),\n    {}\n  );\n}\n\n// src/convert-async-iterator-to-readable-stream.ts\nfunction convertAsyncIteratorToReadableStream(iterator) {\n  return new ReadableStream({\n    /**\n     * Called when the consumer wants to pull more data from the stream.\n     *\n     * @param {ReadableStreamDefaultController<T>} controller - The controller to enqueue data into the stream.\n     * @returns {Promise<void>}\n     */\n    async pull(controller) {\n      try {\n        const { value, done } = await iterator.next();\n        if (done) {\n          controller.close();\n        } else {\n          controller.enqueue(value);\n        }\n      } catch (error) {\n        controller.error(error);\n      }\n    },\n    /**\n     * Called when the consumer cancels the stream.\n     */\n    cancel() {\n    }\n  });\n}\n\n// src/delay.ts\nasync function delay(delayInMs) {\n  return delayInMs == null ? Promise.resolve() : new Promise((resolve2) => setTimeout(resolve2, delayInMs));\n}\n\n// src/event-source-parser-stream.ts\nfunction createEventSourceParserStream() {\n  let buffer = \"\";\n  let event = void 0;\n  let data = [];\n  let lastEventId = void 0;\n  let retry = void 0;\n  function parseLine(line, controller) {\n    if (line === \"\") {\n      dispatchEvent(controller);\n      return;\n    }\n    if (line.startsWith(\":\")) {\n      return;\n    }\n    const colonIndex = line.indexOf(\":\");\n    if (colonIndex === -1) {\n      handleField(line, \"\");\n      return;\n    }\n    const field = line.slice(0, colonIndex);\n    const valueStart = colonIndex + 1;\n    const value = valueStart < line.length && line[valueStart] === \" \" ? line.slice(valueStart + 1) : line.slice(valueStart);\n    handleField(field, value);\n  }\n  function dispatchEvent(controller) {\n    if (data.length > 0) {\n      controller.enqueue({\n        event,\n        data: data.join(\"\\n\"),\n        id: lastEventId,\n        retry\n      });\n      data = [];\n      event = void 0;\n      retry = void 0;\n    }\n  }\n  function handleField(field, value) {\n    switch (field) {\n      case \"event\":\n        event = value;\n        break;\n      case \"data\":\n        data.push(value);\n        break;\n      case \"id\":\n        lastEventId = value;\n        break;\n      case \"retry\":\n        const parsedRetry = parseInt(value, 10);\n        if (!isNaN(parsedRetry)) {\n          retry = parsedRetry;\n        }\n        break;\n    }\n  }\n  return new TransformStream({\n    transform(chunk, controller) {\n      const { lines, incompleteLine } = splitLines(buffer, chunk);\n      buffer = incompleteLine;\n      for (let i = 0; i < lines.length; i++) {\n        parseLine(lines[i], controller);\n      }\n    },\n    flush(controller) {\n      parseLine(buffer, controller);\n      dispatchEvent(controller);\n    }\n  });\n}\nfunction splitLines(buffer, chunk) {\n  const lines = [];\n  let currentLine = buffer;\n  for (let i = 0; i < chunk.length; ) {\n    const char = chunk[i++];\n    if (char === \"\\n\") {\n      lines.push(currentLine);\n      currentLine = \"\";\n    } else if (char === \"\\r\") {\n      lines.push(currentLine);\n      currentLine = \"\";\n      if (chunk[i] === \"\\n\") {\n        i++;\n      }\n    } else {\n      currentLine += char;\n    }\n  }\n  return { lines, incompleteLine: currentLine };\n}\n\n// src/extract-response-headers.ts\nfunction extractResponseHeaders(response) {\n  const headers = {};\n  response.headers.forEach((value, key) => {\n    headers[key] = value;\n  });\n  return headers;\n}\n\n// src/generate-id.ts\n\n\nvar createIdGenerator = ({\n  prefix,\n  size: defaultSize = 16,\n  alphabet = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz\",\n  separator = \"-\"\n} = {}) => {\n  const generator = (0,nanoid_non_secure__WEBPACK_IMPORTED_MODULE_0__.customAlphabet)(alphabet, defaultSize);\n  if (prefix == null) {\n    return generator;\n  }\n  if (alphabet.includes(separator)) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.InvalidArgumentError({\n      argument: \"separator\",\n      message: `The separator \"${separator}\" must not be part of the alphabet \"${alphabet}\".`\n    });\n  }\n  return (size) => `${prefix}${separator}${generator(size)}`;\n};\nvar generateId = createIdGenerator();\n\n// src/get-error-message.ts\nfunction getErrorMessage(error) {\n  if (error == null) {\n    return \"unknown error\";\n  }\n  if (typeof error === \"string\") {\n    return error;\n  }\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return JSON.stringify(error);\n}\n\n// src/get-from-api.ts\n\n\n// src/remove-undefined-entries.ts\nfunction removeUndefinedEntries(record) {\n  return Object.fromEntries(\n    Object.entries(record).filter(([_key, value]) => value != null)\n  );\n}\n\n// src/is-abort-error.ts\nfunction isAbortError(error) {\n  return error instanceof Error && (error.name === \"AbortError\" || error.name === \"TimeoutError\");\n}\n\n// src/get-from-api.ts\nvar getOriginalFetch = () => globalThis.fetch;\nvar getFromApi = async ({\n  url,\n  headers = {},\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch()\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: \"GET\",\n      headers: removeUndefinedEntries(headers),\n      signal: abortSignal\n    });\n    const responseHeaders = extractResponseHeaders(response);\n    if (!response.ok) {\n      let errorInformation;\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: {}\n        });\n      } catch (error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isInstance(error)) {\n          throw error;\n        }\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: \"Failed to process error response\",\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: {}\n        });\n      }\n      throw errorInformation.value;\n    }\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: {}\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: \"Failed to process successful response\",\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: {}\n      });\n    }\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error;\n    }\n    if (error instanceof TypeError && error.message === \"fetch failed\") {\n      const cause = error.cause;\n      if (cause != null) {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: `Cannot connect to API: ${cause.message}`,\n          cause,\n          url,\n          isRetryable: true,\n          requestBodyValues: {}\n        });\n      }\n    }\n    throw error;\n  }\n};\n\n// src/load-api-key.ts\n\nfunction loadApiKey({\n  apiKey,\n  environmentVariableName,\n  apiKeyParameterName = \"apiKey\",\n  description\n}) {\n  if (typeof apiKey === \"string\") {\n    return apiKey;\n  }\n  if (apiKey != null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key must be a string.`\n    });\n  }\n  if (typeof process === \"undefined\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter. Environment variables is not supported in this environment.`\n    });\n  }\n  apiKey = process.env[environmentVariableName];\n  if (apiKey == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter or the ${environmentVariableName} environment variable.`\n    });\n  }\n  if (typeof apiKey !== \"string\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key must be a string. The value of the ${environmentVariableName} environment variable is not a string.`\n    });\n  }\n  return apiKey;\n}\n\n// src/load-optional-setting.ts\nfunction loadOptionalSetting({\n  settingValue,\n  environmentVariableName\n}) {\n  if (typeof settingValue === \"string\") {\n    return settingValue;\n  }\n  if (settingValue != null || typeof process === \"undefined\") {\n    return void 0;\n  }\n  settingValue = process.env[environmentVariableName];\n  if (settingValue == null || typeof settingValue !== \"string\") {\n    return void 0;\n  }\n  return settingValue;\n}\n\n// src/load-setting.ts\n\nfunction loadSetting({\n  settingValue,\n  environmentVariableName,\n  settingName,\n  description\n}) {\n  if (typeof settingValue === \"string\") {\n    return settingValue;\n  }\n  if (settingValue != null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting must be a string.`\n    });\n  }\n  if (typeof process === \"undefined\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting is missing. Pass it using the '${settingName}' parameter. Environment variables is not supported in this environment.`\n    });\n  }\n  settingValue = process.env[environmentVariableName];\n  if (settingValue == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting is missing. Pass it using the '${settingName}' parameter or the ${environmentVariableName} environment variable.`\n    });\n  }\n  if (typeof settingValue !== \"string\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting must be a string. The value of the ${environmentVariableName} environment variable is not a string.`\n    });\n  }\n  return settingValue;\n}\n\n// src/parse-json.ts\n\n\n\n// src/validate-types.ts\n\n\n// src/validator.ts\nvar validatorSymbol = Symbol.for(\"vercel.ai.validator\");\nfunction validator(validate) {\n  return { [validatorSymbol]: true, validate };\n}\nfunction isValidator(value) {\n  return typeof value === \"object\" && value !== null && validatorSymbol in value && value[validatorSymbol] === true && \"validate\" in value;\n}\nfunction asValidator(value) {\n  return isValidator(value) ? value : zodValidator(value);\n}\nfunction zodValidator(zodSchema) {\n  return validator((value) => {\n    const result = zodSchema.safeParse(value);\n    return result.success ? { success: true, value: result.data } : { success: false, error: result.error };\n  });\n}\n\n// src/validate-types.ts\nfunction validateTypes({\n  value,\n  schema: inputSchema\n}) {\n  const result = safeValidateTypes({ value, schema: inputSchema });\n  if (!result.success) {\n    throw _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.wrap({ value, cause: result.error });\n  }\n  return result.value;\n}\nfunction safeValidateTypes({\n  value,\n  schema\n}) {\n  const validator2 = asValidator(schema);\n  try {\n    if (validator2.validate == null) {\n      return { success: true, value };\n    }\n    const result = validator2.validate(value);\n    if (result.success) {\n      return result;\n    }\n    return {\n      success: false,\n      error: _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.wrap({ value, cause: result.error })\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.wrap({ value, cause: error })\n    };\n  }\n}\n\n// src/parse-json.ts\nfunction parseJSON({\n  text,\n  schema\n}) {\n  try {\n    const value = secure_json_parse__WEBPACK_IMPORTED_MODULE_2__.parse(text);\n    if (schema == null) {\n      return value;\n    }\n    return validateTypes({ value, schema });\n  } catch (error) {\n    if (_ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError.isInstance(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.isInstance(error)) {\n      throw error;\n    }\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError({ text, cause: error });\n  }\n}\nfunction safeParseJSON({\n  text,\n  schema\n}) {\n  try {\n    const value = secure_json_parse__WEBPACK_IMPORTED_MODULE_2__.parse(text);\n    if (schema == null) {\n      return { success: true, value, rawValue: value };\n    }\n    const validationResult = safeValidateTypes({ value, schema });\n    return validationResult.success ? { ...validationResult, rawValue: value } : validationResult;\n  } catch (error) {\n    return {\n      success: false,\n      error: _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError.isInstance(error) ? error : new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError({ text, cause: error })\n    };\n  }\n}\nfunction isParsableJson(input) {\n  try {\n    secure_json_parse__WEBPACK_IMPORTED_MODULE_2__.parse(input);\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n// src/parse-provider-options.ts\n\nfunction parseProviderOptions({\n  provider,\n  providerOptions,\n  schema\n}) {\n  if ((providerOptions == null ? void 0 : providerOptions[provider]) == null) {\n    return void 0;\n  }\n  const parsedProviderOptions = safeValidateTypes({\n    value: providerOptions[provider],\n    schema\n  });\n  if (!parsedProviderOptions.success) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.InvalidArgumentError({\n      argument: \"providerOptions\",\n      message: `invalid ${provider} provider options`,\n      cause: parsedProviderOptions.error\n    });\n  }\n  return parsedProviderOptions.value;\n}\n\n// src/post-to-api.ts\n\nvar getOriginalFetch2 = () => globalThis.fetch;\nvar postJsonToApi = async ({\n  url,\n  headers,\n  body,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch\n}) => postToApi({\n  url,\n  headers: {\n    \"Content-Type\": \"application/json\",\n    ...headers\n  },\n  body: {\n    content: JSON.stringify(body),\n    values: body\n  },\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch\n});\nvar postFormDataToApi = async ({\n  url,\n  headers,\n  formData,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch\n}) => postToApi({\n  url,\n  headers,\n  body: {\n    content: formData,\n    values: Object.fromEntries(formData.entries())\n  },\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch\n});\nvar postToApi = async ({\n  url,\n  headers = {},\n  body,\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch2()\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: \"POST\",\n      headers: removeUndefinedEntries(headers),\n      body: body.content,\n      signal: abortSignal\n    });\n    const responseHeaders = extractResponseHeaders(response);\n    if (!response.ok) {\n      let errorInformation;\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: body.values\n        });\n      } catch (error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isInstance(error)) {\n          throw error;\n        }\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: \"Failed to process error response\",\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: body.values\n        });\n      }\n      throw errorInformation.value;\n    }\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: body.values\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: \"Failed to process successful response\",\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: body.values\n      });\n    }\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error;\n    }\n    if (error instanceof TypeError && error.message === \"fetch failed\") {\n      const cause = error.cause;\n      if (cause != null) {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: `Cannot connect to API: ${cause.message}`,\n          cause,\n          url,\n          requestBodyValues: body.values,\n          isRetryable: true\n          // retry when network error\n        });\n      }\n    }\n    throw error;\n  }\n};\n\n// src/resolve.ts\nasync function resolve(value) {\n  if (typeof value === \"function\") {\n    value = value();\n  }\n  return Promise.resolve(value);\n}\n\n// src/response-handler.ts\n\nvar createJsonErrorResponseHandler = ({\n  errorSchema,\n  errorToMessage,\n  isRetryable\n}) => async ({ response, url, requestBodyValues }) => {\n  const responseBody = await response.text();\n  const responseHeaders = extractResponseHeaders(response);\n  if (responseBody.trim() === \"\") {\n    return {\n      responseHeaders,\n      value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: response.statusText,\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        isRetryable: isRetryable == null ? void 0 : isRetryable(response)\n      })\n    };\n  }\n  try {\n    const parsedError = parseJSON({\n      text: responseBody,\n      schema: errorSchema\n    });\n    return {\n      responseHeaders,\n      value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: errorToMessage(parsedError),\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        data: parsedError,\n        isRetryable: isRetryable == null ? void 0 : isRetryable(response, parsedError)\n      })\n    };\n  } catch (parseError) {\n    return {\n      responseHeaders,\n      value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: response.statusText,\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        isRetryable: isRetryable == null ? void 0 : isRetryable(response)\n      })\n    };\n  }\n};\nvar createEventSourceResponseHandler = (chunkSchema) => async ({ response }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  if (response.body == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.EmptyResponseBodyError({});\n  }\n  return {\n    responseHeaders,\n    value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(createEventSourceParserStream()).pipeThrough(\n      new TransformStream({\n        transform({ data }, controller) {\n          if (data === \"[DONE]\") {\n            return;\n          }\n          controller.enqueue(\n            safeParseJSON({\n              text: data,\n              schema: chunkSchema\n            })\n          );\n        }\n      })\n    )\n  };\n};\nvar createJsonStreamResponseHandler = (chunkSchema) => async ({ response }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  if (response.body == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.EmptyResponseBodyError({});\n  }\n  let buffer = \"\";\n  return {\n    responseHeaders,\n    value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(\n      new TransformStream({\n        transform(chunkText, controller) {\n          if (chunkText.endsWith(\"\\n\")) {\n            controller.enqueue(\n              safeParseJSON({\n                text: buffer + chunkText,\n                schema: chunkSchema\n              })\n            );\n            buffer = \"\";\n          } else {\n            buffer += chunkText;\n          }\n        }\n      })\n    )\n  };\n};\nvar createJsonResponseHandler = (responseSchema) => async ({ response, url, requestBodyValues }) => {\n  const responseBody = await response.text();\n  const parsedResult = safeParseJSON({\n    text: responseBody,\n    schema: responseSchema\n  });\n  const responseHeaders = extractResponseHeaders(response);\n  if (!parsedResult.success) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n      message: \"Invalid JSON response\",\n      cause: parsedResult.error,\n      statusCode: response.status,\n      responseHeaders,\n      responseBody,\n      url,\n      requestBodyValues\n    });\n  }\n  return {\n    responseHeaders,\n    value: parsedResult.value,\n    rawValue: parsedResult.rawValue\n  };\n};\nvar createBinaryResponseHandler = () => async ({ response, url, requestBodyValues }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  if (!response.body) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n      message: \"Response body is empty\",\n      url,\n      requestBodyValues,\n      statusCode: response.status,\n      responseHeaders,\n      responseBody: void 0\n    });\n  }\n  try {\n    const buffer = await response.arrayBuffer();\n    return {\n      responseHeaders,\n      value: new Uint8Array(buffer)\n    };\n  } catch (error) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n      message: \"Failed to read response as array buffer\",\n      url,\n      requestBodyValues,\n      statusCode: response.status,\n      responseHeaders,\n      responseBody: void 0,\n      cause: error\n    });\n  }\n};\nvar createStatusCodeErrorResponseHandler = () => async ({ response, url, requestBodyValues }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  const responseBody = await response.text();\n  return {\n    responseHeaders,\n    value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n      message: response.statusText,\n      url,\n      requestBodyValues,\n      statusCode: response.status,\n      responseHeaders,\n      responseBody\n    })\n  };\n};\n\n// src/uint8-utils.ts\nvar { btoa, atob } = globalThis;\nfunction convertBase64ToUint8Array(base64String) {\n  const base64Url = base64String.replace(/-/g, \"+\").replace(/_/g, \"/\");\n  const latin1string = atob(base64Url);\n  return Uint8Array.from(latin1string, (byte) => byte.codePointAt(0));\n}\nfunction convertUint8ArrayToBase64(array) {\n  let latin1string = \"\";\n  for (let i = 0; i < array.length; i++) {\n    latin1string += String.fromCodePoint(array[i]);\n  }\n  return btoa(latin1string);\n}\n\n// src/without-trailing-slash.ts\nfunction withoutTrailingSlash(url) {\n  return url == null ? void 0 : url.replace(/\\/$/, \"\");\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ai-sdk/provider-utils/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ai-sdk/provider/dist/index.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@ai-sdk/provider/dist/index.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AISDKError: () => (/* binding */ AISDKError),\n/* harmony export */   APICallError: () => (/* binding */ APICallError),\n/* harmony export */   EmptyResponseBodyError: () => (/* binding */ EmptyResponseBodyError),\n/* harmony export */   InvalidArgumentError: () => (/* binding */ InvalidArgumentError),\n/* harmony export */   InvalidPromptError: () => (/* binding */ InvalidPromptError),\n/* harmony export */   InvalidResponseDataError: () => (/* binding */ InvalidResponseDataError),\n/* harmony export */   JSONParseError: () => (/* binding */ JSONParseError),\n/* harmony export */   LoadAPIKeyError: () => (/* binding */ LoadAPIKeyError),\n/* harmony export */   LoadSettingError: () => (/* binding */ LoadSettingError),\n/* harmony export */   NoContentGeneratedError: () => (/* binding */ NoContentGeneratedError),\n/* harmony export */   NoSuchModelError: () => (/* binding */ NoSuchModelError),\n/* harmony export */   TooManyEmbeddingValuesForCallError: () => (/* binding */ TooManyEmbeddingValuesForCallError),\n/* harmony export */   TypeValidationError: () => (/* binding */ TypeValidationError),\n/* harmony export */   UnsupportedFunctionalityError: () => (/* binding */ UnsupportedFunctionalityError),\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage),\n/* harmony export */   isJSONArray: () => (/* binding */ isJSONArray),\n/* harmony export */   isJSONObject: () => (/* binding */ isJSONObject),\n/* harmony export */   isJSONValue: () => (/* binding */ isJSONValue)\n/* harmony export */ });\n// src/errors/ai-sdk-error.ts\nvar marker = \"vercel.ai.error\";\nvar symbol = Symbol.for(marker);\nvar _a;\nvar _AISDKError = class _AISDKError extends Error {\n  /**\n   * Creates an AI SDK Error.\n   *\n   * @param {Object} params - The parameters for creating the error.\n   * @param {string} params.name - The name of the error.\n   * @param {string} params.message - The error message.\n   * @param {unknown} [params.cause] - The underlying cause of the error.\n   */\n  constructor({\n    name: name14,\n    message,\n    cause\n  }) {\n    super(message);\n    this[_a] = true;\n    this.name = name14;\n    this.cause = cause;\n  }\n  /**\n   * Checks if the given error is an AI SDK Error.\n   * @param {unknown} error - The error to check.\n   * @returns {boolean} True if the error is an AI SDK Error, false otherwise.\n   */\n  static isInstance(error) {\n    return _AISDKError.hasMarker(error, marker);\n  }\n  static hasMarker(error, marker15) {\n    const markerSymbol = Symbol.for(marker15);\n    return error != null && typeof error === \"object\" && markerSymbol in error && typeof error[markerSymbol] === \"boolean\" && error[markerSymbol] === true;\n  }\n};\n_a = symbol;\nvar AISDKError = _AISDKError;\n\n// src/errors/api-call-error.ts\nvar name = \"AI_APICallError\";\nvar marker2 = `vercel.ai.error.${name}`;\nvar symbol2 = Symbol.for(marker2);\nvar _a2;\nvar APICallError = class extends AISDKError {\n  constructor({\n    message,\n    url,\n    requestBodyValues,\n    statusCode,\n    responseHeaders,\n    responseBody,\n    cause,\n    isRetryable = statusCode != null && (statusCode === 408 || // request timeout\n    statusCode === 409 || // conflict\n    statusCode === 429 || // too many requests\n    statusCode >= 500),\n    // server error\n    data\n  }) {\n    super({ name, message, cause });\n    this[_a2] = true;\n    this.url = url;\n    this.requestBodyValues = requestBodyValues;\n    this.statusCode = statusCode;\n    this.responseHeaders = responseHeaders;\n    this.responseBody = responseBody;\n    this.isRetryable = isRetryable;\n    this.data = data;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker2);\n  }\n};\n_a2 = symbol2;\n\n// src/errors/empty-response-body-error.ts\nvar name2 = \"AI_EmptyResponseBodyError\";\nvar marker3 = `vercel.ai.error.${name2}`;\nvar symbol3 = Symbol.for(marker3);\nvar _a3;\nvar EmptyResponseBodyError = class extends AISDKError {\n  // used in isInstance\n  constructor({ message = \"Empty response body\" } = {}) {\n    super({ name: name2, message });\n    this[_a3] = true;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker3);\n  }\n};\n_a3 = symbol3;\n\n// src/errors/get-error-message.ts\nfunction getErrorMessage(error) {\n  if (error == null) {\n    return \"unknown error\";\n  }\n  if (typeof error === \"string\") {\n    return error;\n  }\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return JSON.stringify(error);\n}\n\n// src/errors/invalid-argument-error.ts\nvar name3 = \"AI_InvalidArgumentError\";\nvar marker4 = `vercel.ai.error.${name3}`;\nvar symbol4 = Symbol.for(marker4);\nvar _a4;\nvar InvalidArgumentError = class extends AISDKError {\n  constructor({\n    message,\n    cause,\n    argument\n  }) {\n    super({ name: name3, message, cause });\n    this[_a4] = true;\n    this.argument = argument;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker4);\n  }\n};\n_a4 = symbol4;\n\n// src/errors/invalid-prompt-error.ts\nvar name4 = \"AI_InvalidPromptError\";\nvar marker5 = `vercel.ai.error.${name4}`;\nvar symbol5 = Symbol.for(marker5);\nvar _a5;\nvar InvalidPromptError = class extends AISDKError {\n  constructor({\n    prompt,\n    message,\n    cause\n  }) {\n    super({ name: name4, message: `Invalid prompt: ${message}`, cause });\n    this[_a5] = true;\n    this.prompt = prompt;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker5);\n  }\n};\n_a5 = symbol5;\n\n// src/errors/invalid-response-data-error.ts\nvar name5 = \"AI_InvalidResponseDataError\";\nvar marker6 = `vercel.ai.error.${name5}`;\nvar symbol6 = Symbol.for(marker6);\nvar _a6;\nvar InvalidResponseDataError = class extends AISDKError {\n  constructor({\n    data,\n    message = `Invalid response data: ${JSON.stringify(data)}.`\n  }) {\n    super({ name: name5, message });\n    this[_a6] = true;\n    this.data = data;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker6);\n  }\n};\n_a6 = symbol6;\n\n// src/errors/json-parse-error.ts\nvar name6 = \"AI_JSONParseError\";\nvar marker7 = `vercel.ai.error.${name6}`;\nvar symbol7 = Symbol.for(marker7);\nvar _a7;\nvar JSONParseError = class extends AISDKError {\n  constructor({ text, cause }) {\n    super({\n      name: name6,\n      message: `JSON parsing failed: Text: ${text}.\nError message: ${getErrorMessage(cause)}`,\n      cause\n    });\n    this[_a7] = true;\n    this.text = text;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker7);\n  }\n};\n_a7 = symbol7;\n\n// src/errors/load-api-key-error.ts\nvar name7 = \"AI_LoadAPIKeyError\";\nvar marker8 = `vercel.ai.error.${name7}`;\nvar symbol8 = Symbol.for(marker8);\nvar _a8;\nvar LoadAPIKeyError = class extends AISDKError {\n  // used in isInstance\n  constructor({ message }) {\n    super({ name: name7, message });\n    this[_a8] = true;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker8);\n  }\n};\n_a8 = symbol8;\n\n// src/errors/load-setting-error.ts\nvar name8 = \"AI_LoadSettingError\";\nvar marker9 = `vercel.ai.error.${name8}`;\nvar symbol9 = Symbol.for(marker9);\nvar _a9;\nvar LoadSettingError = class extends AISDKError {\n  // used in isInstance\n  constructor({ message }) {\n    super({ name: name8, message });\n    this[_a9] = true;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker9);\n  }\n};\n_a9 = symbol9;\n\n// src/errors/no-content-generated-error.ts\nvar name9 = \"AI_NoContentGeneratedError\";\nvar marker10 = `vercel.ai.error.${name9}`;\nvar symbol10 = Symbol.for(marker10);\nvar _a10;\nvar NoContentGeneratedError = class extends AISDKError {\n  // used in isInstance\n  constructor({\n    message = \"No content generated.\"\n  } = {}) {\n    super({ name: name9, message });\n    this[_a10] = true;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker10);\n  }\n};\n_a10 = symbol10;\n\n// src/errors/no-such-model-error.ts\nvar name10 = \"AI_NoSuchModelError\";\nvar marker11 = `vercel.ai.error.${name10}`;\nvar symbol11 = Symbol.for(marker11);\nvar _a11;\nvar NoSuchModelError = class extends AISDKError {\n  constructor({\n    errorName = name10,\n    modelId,\n    modelType,\n    message = `No such ${modelType}: ${modelId}`\n  }) {\n    super({ name: errorName, message });\n    this[_a11] = true;\n    this.modelId = modelId;\n    this.modelType = modelType;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker11);\n  }\n};\n_a11 = symbol11;\n\n// src/errors/too-many-embedding-values-for-call-error.ts\nvar name11 = \"AI_TooManyEmbeddingValuesForCallError\";\nvar marker12 = `vercel.ai.error.${name11}`;\nvar symbol12 = Symbol.for(marker12);\nvar _a12;\nvar TooManyEmbeddingValuesForCallError = class extends AISDKError {\n  constructor(options) {\n    super({\n      name: name11,\n      message: `Too many values for a single embedding call. The ${options.provider} model \"${options.modelId}\" can only embed up to ${options.maxEmbeddingsPerCall} values per call, but ${options.values.length} values were provided.`\n    });\n    this[_a12] = true;\n    this.provider = options.provider;\n    this.modelId = options.modelId;\n    this.maxEmbeddingsPerCall = options.maxEmbeddingsPerCall;\n    this.values = options.values;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker12);\n  }\n};\n_a12 = symbol12;\n\n// src/errors/type-validation-error.ts\nvar name12 = \"AI_TypeValidationError\";\nvar marker13 = `vercel.ai.error.${name12}`;\nvar symbol13 = Symbol.for(marker13);\nvar _a13;\nvar _TypeValidationError = class _TypeValidationError extends AISDKError {\n  constructor({ value, cause }) {\n    super({\n      name: name12,\n      message: `Type validation failed: Value: ${JSON.stringify(value)}.\nError message: ${getErrorMessage(cause)}`,\n      cause\n    });\n    this[_a13] = true;\n    this.value = value;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker13);\n  }\n  /**\n   * Wraps an error into a TypeValidationError.\n   * If the cause is already a TypeValidationError with the same value, it returns the cause.\n   * Otherwise, it creates a new TypeValidationError.\n   *\n   * @param {Object} params - The parameters for wrapping the error.\n   * @param {unknown} params.value - The value that failed validation.\n   * @param {unknown} params.cause - The original error or cause of the validation failure.\n   * @returns {TypeValidationError} A TypeValidationError instance.\n   */\n  static wrap({\n    value,\n    cause\n  }) {\n    return _TypeValidationError.isInstance(cause) && cause.value === value ? cause : new _TypeValidationError({ value, cause });\n  }\n};\n_a13 = symbol13;\nvar TypeValidationError = _TypeValidationError;\n\n// src/errors/unsupported-functionality-error.ts\nvar name13 = \"AI_UnsupportedFunctionalityError\";\nvar marker14 = `vercel.ai.error.${name13}`;\nvar symbol14 = Symbol.for(marker14);\nvar _a14;\nvar UnsupportedFunctionalityError = class extends AISDKError {\n  constructor({\n    functionality,\n    message = `'${functionality}' functionality not supported.`\n  }) {\n    super({ name: name13, message });\n    this[_a14] = true;\n    this.functionality = functionality;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker14);\n  }\n};\n_a14 = symbol14;\n\n// src/json-value/is-json.ts\nfunction isJSONValue(value) {\n  if (value === null || typeof value === \"string\" || typeof value === \"number\" || typeof value === \"boolean\") {\n    return true;\n  }\n  if (Array.isArray(value)) {\n    return value.every(isJSONValue);\n  }\n  if (typeof value === \"object\") {\n    return Object.entries(value).every(\n      ([key, val]) => typeof key === \"string\" && isJSONValue(val)\n    );\n  }\n  return false;\n}\nfunction isJSONArray(value) {\n  return Array.isArray(value) && value.every(isJSONValue);\n}\nfunction isJSONObject(value) {\n  return value != null && typeof value === \"object\" && Object.entries(value).every(\n    ([key, val]) => typeof key === \"string\" && isJSONValue(val)\n  );\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ai-sdk/provider/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ai-sdk/react/dist/index.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@ai-sdk/react/dist/index.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   experimental_useObject: () => (/* binding */ experimental_useObject),\n/* harmony export */   useAssistant: () => (/* binding */ useAssistant),\n/* harmony export */   useChat: () => (/* binding */ useChat),\n/* harmony export */   useCompletion: () => (/* binding */ useCompletion)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ai-sdk/provider-utils */ \"(ssr)/./node_modules/@ai-sdk/provider-utils/dist/index.mjs\");\n/* harmony import */ var _ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ai-sdk/ui-utils */ \"(ssr)/./node_modules/@ai-sdk/ui-utils/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var swr__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! swr */ \"(ssr)/./node_modules/swr/dist/index/index.mjs\");\n/* harmony import */ var throttleit__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! throttleit */ \"(ssr)/./node_modules/throttleit/index.js\");\n// src/use-assistant.ts\n\n\n\nvar getOriginalFetch = () => fetch;\nfunction useAssistant({\n  api,\n  threadId: threadIdParam,\n  credentials,\n  headers,\n  body,\n  onError,\n  fetch: fetch2\n}) {\n  const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [currentThreadId, setCurrentThreadId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    void 0\n  );\n  const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"awaiting_message\");\n  const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(void 0);\n  const handleInputChange = (event) => {\n    setInput(event.target.value);\n  };\n  const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n      abortControllerRef.current = null;\n    }\n  }, []);\n  const append = async (message, requestOptions) => {\n    var _a, _b;\n    setStatus(\"in_progress\");\n    setMessages((messages2) => {\n      var _a2;\n      return [\n        ...messages2,\n        {\n          ...message,\n          id: (_a2 = message.id) != null ? _a2 : (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.generateId)()\n        }\n      ];\n    });\n    setInput(\"\");\n    const abortController = new AbortController();\n    try {\n      abortControllerRef.current = abortController;\n      const actualFetch = fetch2 != null ? fetch2 : getOriginalFetch();\n      const response = await actualFetch(api, {\n        method: \"POST\",\n        credentials,\n        signal: abortController.signal,\n        headers: { \"Content-Type\": \"application/json\", ...headers },\n        body: JSON.stringify({\n          ...body,\n          // always use user-provided threadId when available:\n          threadId: (_a = threadIdParam != null ? threadIdParam : currentThreadId) != null ? _a : null,\n          message: message.content,\n          // optional request data:\n          data: requestOptions == null ? void 0 : requestOptions.data\n        })\n      });\n      if (!response.ok) {\n        throw new Error(\n          (_b = await response.text()) != null ? _b : \"Failed to fetch the assistant response.\"\n        );\n      }\n      if (response.body == null) {\n        throw new Error(\"The response body is empty.\");\n      }\n      await (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.processAssistantStream)({\n        stream: response.body,\n        onAssistantMessagePart(value) {\n          setMessages((messages2) => [\n            ...messages2,\n            {\n              id: value.id,\n              role: value.role,\n              content: value.content[0].text.value,\n              parts: []\n            }\n          ]);\n        },\n        onTextPart(value) {\n          setMessages((messages2) => {\n            const lastMessage = messages2[messages2.length - 1];\n            return [\n              ...messages2.slice(0, messages2.length - 1),\n              {\n                id: lastMessage.id,\n                role: lastMessage.role,\n                content: lastMessage.content + value,\n                parts: lastMessage.parts\n              }\n            ];\n          });\n        },\n        onAssistantControlDataPart(value) {\n          setCurrentThreadId(value.threadId);\n          setMessages((messages2) => {\n            const lastMessage = messages2[messages2.length - 1];\n            lastMessage.id = value.messageId;\n            return [...messages2.slice(0, messages2.length - 1), lastMessage];\n          });\n        },\n        onDataMessagePart(value) {\n          setMessages((messages2) => {\n            var _a2;\n            return [\n              ...messages2,\n              {\n                id: (_a2 = value.id) != null ? _a2 : (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.generateId)(),\n                role: \"data\",\n                content: \"\",\n                data: value.data,\n                parts: []\n              }\n            ];\n          });\n        },\n        onErrorPart(value) {\n          setError(new Error(value));\n        }\n      });\n    } catch (error2) {\n      if ((0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.isAbortError)(error2) && abortController.signal.aborted) {\n        abortControllerRef.current = null;\n        return;\n      }\n      if (onError && error2 instanceof Error) {\n        onError(error2);\n      }\n      setError(error2);\n    } finally {\n      abortControllerRef.current = null;\n      setStatus(\"awaiting_message\");\n    }\n  };\n  const submitMessage = async (event, requestOptions) => {\n    var _a;\n    (_a = event == null ? void 0 : event.preventDefault) == null ? void 0 : _a.call(event);\n    if (input === \"\") {\n      return;\n    }\n    append({ role: \"user\", content: input, parts: [] }, requestOptions);\n  };\n  const setThreadId = (threadId) => {\n    setCurrentThreadId(threadId);\n    setMessages([]);\n  };\n  return {\n    append,\n    messages,\n    setMessages,\n    threadId: currentThreadId,\n    setThreadId,\n    input,\n    setInput,\n    handleInputChange,\n    submitMessage,\n    status,\n    error,\n    stop\n  };\n}\n\n// src/use-chat.ts\n\n\n\n\n// src/throttle.ts\n\nfunction throttle(fn, waitMs) {\n  return waitMs != null ? throttleit__WEBPACK_IMPORTED_MODULE_3__(fn, waitMs) : fn;\n}\n\n// src/util/use-stable-value.ts\n\n\nfunction useStableValue(latestValue) {\n  const [value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(latestValue);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!(0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.isDeepEqualData)(latestValue, value)) {\n      setValue(latestValue);\n    }\n  }, [latestValue, value]);\n  return value;\n}\n\n// src/use-chat.ts\nfunction useChat({\n  api = \"/api/chat\",\n  id,\n  initialMessages,\n  initialInput = \"\",\n  sendExtraMessageFields,\n  onToolCall,\n  experimental_prepareRequestBody,\n  maxSteps = 1,\n  streamProtocol = \"data\",\n  onResponse,\n  onFinish,\n  onError,\n  credentials,\n  headers,\n  body,\n  generateId: generateId2 = _ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.generateId,\n  fetch: fetch2,\n  keepLastMessageOnError = true,\n  experimental_throttle: throttleWaitMs\n} = {}) {\n  const [hookId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(generateId2);\n  const chatId = id != null ? id : hookId;\n  const chatKey = typeof api === \"string\" ? [api, chatId] : chatId;\n  const stableInitialMessages = useStableValue(initialMessages != null ? initialMessages : []);\n  const processedInitialMessages = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.fillMessageParts)(stableInitialMessages),\n    [stableInitialMessages]\n  );\n  const { data: messages, mutate } = (0,swr__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(\n    [chatKey, \"messages\"],\n    null,\n    { fallbackData: processedInitialMessages }\n  );\n  const messagesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(messages || []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    messagesRef.current = messages || [];\n  }, [messages]);\n  const { data: streamData, mutate: mutateStreamData } = (0,swr__WEBPACK_IMPORTED_MODULE_4__[\"default\"])([chatKey, \"streamData\"], null);\n  const streamDataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(streamData);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    streamDataRef.current = streamData;\n  }, [streamData]);\n  const { data: status = \"ready\", mutate: mutateStatus } = (0,swr__WEBPACK_IMPORTED_MODULE_4__[\"default\"])([chatKey, \"status\"], null);\n  const { data: error = void 0, mutate: setError } = (0,swr__WEBPACK_IMPORTED_MODULE_4__[\"default\"])([chatKey, \"error\"], null);\n  const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const extraMetadataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    credentials,\n    headers,\n    body\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    extraMetadataRef.current = {\n      credentials,\n      headers,\n      body\n    };\n  }, [credentials, headers, body]);\n  const triggerRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async (chatRequest, requestType = \"generate\") => {\n      var _a, _b;\n      mutateStatus(\"submitted\");\n      setError(void 0);\n      const chatMessages = (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.fillMessageParts)(chatRequest.messages);\n      const messageCount = chatMessages.length;\n      const maxStep = (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.extractMaxToolInvocationStep)(\n        (_a = chatMessages[chatMessages.length - 1]) == null ? void 0 : _a.toolInvocations\n      );\n      try {\n        const abortController = new AbortController();\n        abortControllerRef.current = abortController;\n        const throttledMutate = throttle(mutate, throttleWaitMs);\n        const throttledMutateStreamData = throttle(\n          mutateStreamData,\n          throttleWaitMs\n        );\n        const previousMessages = messagesRef.current;\n        throttledMutate(chatMessages, false);\n        const constructedMessagesPayload = sendExtraMessageFields ? chatMessages : chatMessages.map(\n          ({\n            role,\n            content,\n            experimental_attachments,\n            data,\n            annotations,\n            toolInvocations,\n            parts\n          }) => ({\n            role,\n            content,\n            ...experimental_attachments !== void 0 && {\n              experimental_attachments\n            },\n            ...data !== void 0 && { data },\n            ...annotations !== void 0 && { annotations },\n            ...toolInvocations !== void 0 && { toolInvocations },\n            ...parts !== void 0 && { parts }\n          })\n        );\n        const existingData = streamDataRef.current;\n        await (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.callChatApi)({\n          api,\n          body: (_b = experimental_prepareRequestBody == null ? void 0 : experimental_prepareRequestBody({\n            id: chatId,\n            messages: chatMessages,\n            requestData: chatRequest.data,\n            requestBody: chatRequest.body\n          })) != null ? _b : {\n            id: chatId,\n            messages: constructedMessagesPayload,\n            data: chatRequest.data,\n            ...extraMetadataRef.current.body,\n            ...chatRequest.body\n          },\n          streamProtocol,\n          credentials: extraMetadataRef.current.credentials,\n          headers: {\n            ...extraMetadataRef.current.headers,\n            ...chatRequest.headers\n          },\n          abortController: () => abortControllerRef.current,\n          restoreMessagesOnFailure() {\n            if (!keepLastMessageOnError) {\n              throttledMutate(previousMessages, false);\n            }\n          },\n          onResponse,\n          onUpdate({ message, data, replaceLastMessage }) {\n            mutateStatus(\"streaming\");\n            throttledMutate(\n              [\n                ...replaceLastMessage ? chatMessages.slice(0, chatMessages.length - 1) : chatMessages,\n                message\n              ],\n              false\n            );\n            if (data == null ? void 0 : data.length) {\n              throttledMutateStreamData(\n                [...existingData != null ? existingData : [], ...data],\n                false\n              );\n            }\n          },\n          onToolCall,\n          onFinish,\n          generateId: generateId2,\n          fetch: fetch2,\n          lastMessage: chatMessages[chatMessages.length - 1],\n          requestType\n        });\n        abortControllerRef.current = null;\n        mutateStatus(\"ready\");\n      } catch (err) {\n        if (err.name === \"AbortError\") {\n          abortControllerRef.current = null;\n          mutateStatus(\"ready\");\n          return null;\n        }\n        if (onError && err instanceof Error) {\n          onError(err);\n        }\n        setError(err);\n        mutateStatus(\"error\");\n      }\n      const messages2 = messagesRef.current;\n      if ((0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.shouldResubmitMessages)({\n        originalMaxToolInvocationStep: maxStep,\n        originalMessageCount: messageCount,\n        maxSteps,\n        messages: messages2\n      })) {\n        await triggerRequest({ messages: messages2 });\n      }\n    },\n    [\n      mutate,\n      mutateStatus,\n      api,\n      extraMetadataRef,\n      onResponse,\n      onFinish,\n      onError,\n      setError,\n      mutateStreamData,\n      streamDataRef,\n      streamProtocol,\n      sendExtraMessageFields,\n      experimental_prepareRequestBody,\n      onToolCall,\n      maxSteps,\n      messagesRef,\n      abortControllerRef,\n      generateId2,\n      fetch2,\n      keepLastMessageOnError,\n      throttleWaitMs,\n      chatId\n    ]\n  );\n  const append = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async (message, {\n      data,\n      headers: headers2,\n      body: body2,\n      experimental_attachments = message.experimental_attachments\n    } = {}) => {\n      var _a, _b;\n      const attachmentsForRequest = await (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.prepareAttachmentsForRequest)(\n        experimental_attachments\n      );\n      const messages2 = messagesRef.current.concat({\n        ...message,\n        id: (_a = message.id) != null ? _a : generateId2(),\n        createdAt: (_b = message.createdAt) != null ? _b : /* @__PURE__ */ new Date(),\n        experimental_attachments: attachmentsForRequest.length > 0 ? attachmentsForRequest : void 0,\n        parts: (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.getMessageParts)(message)\n      });\n      return triggerRequest({ messages: messages2, headers: headers2, body: body2, data });\n    },\n    [triggerRequest, generateId2]\n  );\n  const reload = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async ({ data, headers: headers2, body: body2 } = {}) => {\n      const messages2 = messagesRef.current;\n      if (messages2.length === 0) {\n        return null;\n      }\n      const lastMessage = messages2[messages2.length - 1];\n      return triggerRequest({\n        messages: lastMessage.role === \"assistant\" ? messages2.slice(0, -1) : messages2,\n        headers: headers2,\n        body: body2,\n        data\n      });\n    },\n    [triggerRequest]\n  );\n  const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n      abortControllerRef.current = null;\n    }\n  }, []);\n  const experimental_resume = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async () => {\n    const messages2 = messagesRef.current;\n    triggerRequest({ messages: messages2 }, \"resume\");\n  }, [triggerRequest]);\n  const setMessages = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (messages2) => {\n      if (typeof messages2 === \"function\") {\n        messages2 = messages2(messagesRef.current);\n      }\n      const messagesWithParts = (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.fillMessageParts)(messages2);\n      mutate(messagesWithParts, false);\n      messagesRef.current = messagesWithParts;\n    },\n    [mutate]\n  );\n  const setData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (data) => {\n      if (typeof data === \"function\") {\n        data = data(streamDataRef.current);\n      }\n      mutateStreamData(data, false);\n      streamDataRef.current = data;\n    },\n    [mutateStreamData]\n  );\n  const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialInput);\n  const handleSubmit = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async (event, options = {}, metadata) => {\n      var _a;\n      (_a = event == null ? void 0 : event.preventDefault) == null ? void 0 : _a.call(event);\n      if (!input && !options.allowEmptySubmit)\n        return;\n      if (metadata) {\n        extraMetadataRef.current = {\n          ...extraMetadataRef.current,\n          ...metadata\n        };\n      }\n      const attachmentsForRequest = await (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.prepareAttachmentsForRequest)(\n        options.experimental_attachments\n      );\n      const messages2 = messagesRef.current.concat({\n        id: generateId2(),\n        createdAt: /* @__PURE__ */ new Date(),\n        role: \"user\",\n        content: input,\n        experimental_attachments: attachmentsForRequest.length > 0 ? attachmentsForRequest : void 0,\n        parts: [{ type: \"text\", text: input }]\n      });\n      const chatRequest = {\n        messages: messages2,\n        headers: options.headers,\n        body: options.body,\n        data: options.data\n      };\n      triggerRequest(chatRequest);\n      setInput(\"\");\n    },\n    [input, generateId2, triggerRequest]\n  );\n  const handleInputChange = (e) => {\n    setInput(e.target.value);\n  };\n  const addToolResult = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    ({ toolCallId, result }) => {\n      const currentMessages = messagesRef.current;\n      (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.updateToolCallResult)({\n        messages: currentMessages,\n        toolCallId,\n        toolResult: result\n      });\n      mutate(\n        [\n          ...currentMessages.slice(0, currentMessages.length - 1),\n          { ...currentMessages[currentMessages.length - 1] }\n        ],\n        false\n      );\n      if (status === \"submitted\" || status === \"streaming\") {\n        return;\n      }\n      const lastMessage = currentMessages[currentMessages.length - 1];\n      if ((0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.isAssistantMessageWithCompletedToolCalls)(lastMessage)) {\n        triggerRequest({ messages: currentMessages });\n      }\n    },\n    [mutate, status, triggerRequest]\n  );\n  return {\n    messages: messages != null ? messages : [],\n    id: chatId,\n    setMessages,\n    data: streamData,\n    setData,\n    error,\n    append,\n    reload,\n    stop,\n    experimental_resume,\n    input,\n    setInput,\n    handleInputChange,\n    handleSubmit,\n    isLoading: status === \"submitted\" || status === \"streaming\",\n    status,\n    addToolResult\n  };\n}\n\n// src/use-completion.ts\n\n\n\nfunction useCompletion({\n  api = \"/api/completion\",\n  id,\n  initialCompletion = \"\",\n  initialInput = \"\",\n  credentials,\n  headers,\n  body,\n  streamProtocol = \"data\",\n  fetch: fetch2,\n  onResponse,\n  onFinish,\n  onError,\n  experimental_throttle: throttleWaitMs\n} = {}) {\n  const hookId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useId)();\n  const completionId = id || hookId;\n  const { data, mutate } = (0,swr__WEBPACK_IMPORTED_MODULE_4__[\"default\"])([api, completionId], null, {\n    fallbackData: initialCompletion\n  });\n  const { data: isLoading = false, mutate: mutateLoading } = (0,swr__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(\n    [completionId, \"loading\"],\n    null\n  );\n  const { data: streamData, mutate: mutateStreamData } = (0,swr__WEBPACK_IMPORTED_MODULE_4__[\"default\"])([completionId, \"streamData\"], null);\n  const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(void 0);\n  const completion = data;\n  const [abortController, setAbortController] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const extraMetadataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    credentials,\n    headers,\n    body\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    extraMetadataRef.current = {\n      credentials,\n      headers,\n      body\n    };\n  }, [credentials, headers, body]);\n  const triggerRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async (prompt, options) => (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.callCompletionApi)({\n      api,\n      prompt,\n      credentials: extraMetadataRef.current.credentials,\n      headers: { ...extraMetadataRef.current.headers, ...options == null ? void 0 : options.headers },\n      body: {\n        ...extraMetadataRef.current.body,\n        ...options == null ? void 0 : options.body\n      },\n      streamProtocol,\n      fetch: fetch2,\n      // throttle streamed ui updates:\n      setCompletion: throttle(\n        (completion2) => mutate(completion2, false),\n        throttleWaitMs\n      ),\n      onData: throttle(\n        (data2) => mutateStreamData([...streamData != null ? streamData : [], ...data2 != null ? data2 : []], false),\n        throttleWaitMs\n      ),\n      setLoading: mutateLoading,\n      setError,\n      setAbortController,\n      onResponse,\n      onFinish,\n      onError\n    }),\n    [\n      mutate,\n      mutateLoading,\n      api,\n      extraMetadataRef,\n      setAbortController,\n      onResponse,\n      onFinish,\n      onError,\n      setError,\n      streamData,\n      streamProtocol,\n      fetch2,\n      mutateStreamData,\n      throttleWaitMs\n    ]\n  );\n  const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (abortController) {\n      abortController.abort();\n      setAbortController(null);\n    }\n  }, [abortController]);\n  const setCompletion = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (completion2) => {\n      mutate(completion2, false);\n    },\n    [mutate]\n  );\n  const complete = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async (prompt, options) => {\n      return triggerRequest(prompt, options);\n    },\n    [triggerRequest]\n  );\n  const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialInput);\n  const handleSubmit = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (event) => {\n      var _a;\n      (_a = event == null ? void 0 : event.preventDefault) == null ? void 0 : _a.call(event);\n      return input ? complete(input) : void 0;\n    },\n    [input, complete]\n  );\n  const handleInputChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (e) => {\n      setInput(e.target.value);\n    },\n    [setInput]\n  );\n  return {\n    completion,\n    complete,\n    error,\n    setCompletion,\n    stop,\n    input,\n    setInput,\n    handleInputChange,\n    handleSubmit,\n    isLoading,\n    data: streamData\n  };\n}\n\n// src/use-object.ts\n\n\n\n\nvar getOriginalFetch2 = () => fetch;\nfunction useObject({\n  api,\n  id,\n  schema,\n  // required, in the future we will use it for validation\n  initialValue,\n  fetch: fetch2,\n  onError,\n  onFinish,\n  headers,\n  credentials\n}) {\n  const hookId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useId)();\n  const completionId = id != null ? id : hookId;\n  const { data, mutate } = (0,swr__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(\n    [api, completionId],\n    null,\n    { fallbackData: initialValue }\n  );\n  const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(void 0);\n  const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    var _a;\n    try {\n      (_a = abortControllerRef.current) == null ? void 0 : _a.abort();\n    } catch (ignored) {\n    } finally {\n      setIsLoading(false);\n      abortControllerRef.current = null;\n    }\n  }, []);\n  const submit = async (input) => {\n    var _a;\n    try {\n      mutate(void 0);\n      setIsLoading(true);\n      setError(void 0);\n      const abortController = new AbortController();\n      abortControllerRef.current = abortController;\n      const actualFetch = fetch2 != null ? fetch2 : getOriginalFetch2();\n      const response = await actualFetch(api, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          ...headers\n        },\n        credentials,\n        signal: abortController.signal,\n        body: JSON.stringify(input)\n      });\n      if (!response.ok) {\n        throw new Error(\n          (_a = await response.text()) != null ? _a : \"Failed to fetch the response.\"\n        );\n      }\n      if (response.body == null) {\n        throw new Error(\"The response body is empty.\");\n      }\n      let accumulatedText = \"\";\n      let latestObject = void 0;\n      await response.body.pipeThrough(new TextDecoderStream()).pipeTo(\n        new WritableStream({\n          write(chunk) {\n            accumulatedText += chunk;\n            const { value } = (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.parsePartialJson)(accumulatedText);\n            const currentObject = value;\n            if (!(0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.isDeepEqualData)(latestObject, currentObject)) {\n              latestObject = currentObject;\n              mutate(currentObject);\n            }\n          },\n          close() {\n            setIsLoading(false);\n            abortControllerRef.current = null;\n            if (onFinish != null) {\n              const validationResult = (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.safeValidateTypes)({\n                value: latestObject,\n                schema: (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.asSchema)(schema)\n              });\n              onFinish(\n                validationResult.success ? { object: validationResult.value, error: void 0 } : { object: void 0, error: validationResult.error }\n              );\n            }\n          }\n        })\n      );\n    } catch (error2) {\n      if ((0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.isAbortError)(error2)) {\n        return;\n      }\n      if (onError && error2 instanceof Error) {\n        onError(error2);\n      }\n      setIsLoading(false);\n      setError(error2 instanceof Error ? error2 : new Error(String(error2)));\n    }\n  };\n  return {\n    submit,\n    object: data,\n    error,\n    isLoading,\n    stop\n  };\n}\nvar experimental_useObject = useObject;\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ai-sdk/react/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ai-sdk/ui-utils/dist/index.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@ai-sdk/ui-utils/dist/index.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   asSchema: () => (/* binding */ asSchema),\n/* harmony export */   callChatApi: () => (/* binding */ callChatApi),\n/* harmony export */   callCompletionApi: () => (/* binding */ callCompletionApi),\n/* harmony export */   extractMaxToolInvocationStep: () => (/* binding */ extractMaxToolInvocationStep),\n/* harmony export */   fillMessageParts: () => (/* binding */ fillMessageParts),\n/* harmony export */   formatAssistantStreamPart: () => (/* binding */ formatAssistantStreamPart),\n/* harmony export */   formatDataStreamPart: () => (/* binding */ formatDataStreamPart),\n/* harmony export */   generateId: () => (/* reexport safe */ _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId),\n/* harmony export */   getMessageParts: () => (/* binding */ getMessageParts),\n/* harmony export */   getTextFromDataUrl: () => (/* binding */ getTextFromDataUrl),\n/* harmony export */   isAssistantMessageWithCompletedToolCalls: () => (/* binding */ isAssistantMessageWithCompletedToolCalls),\n/* harmony export */   isDeepEqualData: () => (/* binding */ isDeepEqualData),\n/* harmony export */   jsonSchema: () => (/* binding */ jsonSchema),\n/* harmony export */   parseAssistantStreamPart: () => (/* binding */ parseAssistantStreamPart),\n/* harmony export */   parseDataStreamPart: () => (/* binding */ parseDataStreamPart),\n/* harmony export */   parsePartialJson: () => (/* binding */ parsePartialJson),\n/* harmony export */   prepareAttachmentsForRequest: () => (/* binding */ prepareAttachmentsForRequest),\n/* harmony export */   processAssistantStream: () => (/* binding */ processAssistantStream),\n/* harmony export */   processDataStream: () => (/* binding */ processDataStream),\n/* harmony export */   processTextStream: () => (/* binding */ processTextStream),\n/* harmony export */   shouldResubmitMessages: () => (/* binding */ shouldResubmitMessages),\n/* harmony export */   updateToolCallResult: () => (/* binding */ updateToolCallResult),\n/* harmony export */   zodSchema: () => (/* binding */ zodSchema)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ai-sdk/provider-utils */ \"(ssr)/./node_modules/@ai-sdk/provider-utils/dist/index.mjs\");\n/* harmony import */ var zod_to_json_schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod-to-json-schema */ \"(ssr)/./node_modules/zod-to-json-schema/dist/esm/index.js\");\n// src/index.ts\n\n\n// src/assistant-stream-parts.ts\nvar textStreamPart = {\n  code: \"0\",\n  name: \"text\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"text\" parts expect a string value.');\n    }\n    return { type: \"text\", value };\n  }\n};\nvar errorStreamPart = {\n  code: \"3\",\n  name: \"error\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"error\" parts expect a string value.');\n    }\n    return { type: \"error\", value };\n  }\n};\nvar assistantMessageStreamPart = {\n  code: \"4\",\n  name: \"assistant_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"id\" in value) || !(\"role\" in value) || !(\"content\" in value) || typeof value.id !== \"string\" || typeof value.role !== \"string\" || value.role !== \"assistant\" || !Array.isArray(value.content) || !value.content.every(\n      (item) => item != null && typeof item === \"object\" && \"type\" in item && item.type === \"text\" && \"text\" in item && item.text != null && typeof item.text === \"object\" && \"value\" in item.text && typeof item.text.value === \"string\"\n    )) {\n      throw new Error(\n        '\"assistant_message\" parts expect an object with an \"id\", \"role\", and \"content\" property.'\n      );\n    }\n    return {\n      type: \"assistant_message\",\n      value\n    };\n  }\n};\nvar assistantControlDataStreamPart = {\n  code: \"5\",\n  name: \"assistant_control_data\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"threadId\" in value) || !(\"messageId\" in value) || typeof value.threadId !== \"string\" || typeof value.messageId !== \"string\") {\n      throw new Error(\n        '\"assistant_control_data\" parts expect an object with a \"threadId\" and \"messageId\" property.'\n      );\n    }\n    return {\n      type: \"assistant_control_data\",\n      value: {\n        threadId: value.threadId,\n        messageId: value.messageId\n      }\n    };\n  }\n};\nvar dataMessageStreamPart = {\n  code: \"6\",\n  name: \"data_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"role\" in value) || !(\"data\" in value) || typeof value.role !== \"string\" || value.role !== \"data\") {\n      throw new Error(\n        '\"data_message\" parts expect an object with a \"role\" and \"data\" property.'\n      );\n    }\n    return {\n      type: \"data_message\",\n      value\n    };\n  }\n};\nvar assistantStreamParts = [\n  textStreamPart,\n  errorStreamPart,\n  assistantMessageStreamPart,\n  assistantControlDataStreamPart,\n  dataMessageStreamPart\n];\nvar assistantStreamPartsByCode = {\n  [textStreamPart.code]: textStreamPart,\n  [errorStreamPart.code]: errorStreamPart,\n  [assistantMessageStreamPart.code]: assistantMessageStreamPart,\n  [assistantControlDataStreamPart.code]: assistantControlDataStreamPart,\n  [dataMessageStreamPart.code]: dataMessageStreamPart\n};\nvar StreamStringPrefixes = {\n  [textStreamPart.name]: textStreamPart.code,\n  [errorStreamPart.name]: errorStreamPart.code,\n  [assistantMessageStreamPart.name]: assistantMessageStreamPart.code,\n  [assistantControlDataStreamPart.name]: assistantControlDataStreamPart.code,\n  [dataMessageStreamPart.name]: dataMessageStreamPart.code\n};\nvar validCodes = assistantStreamParts.map((part) => part.code);\nvar parseAssistantStreamPart = (line) => {\n  const firstSeparatorIndex = line.indexOf(\":\");\n  if (firstSeparatorIndex === -1) {\n    throw new Error(\"Failed to parse stream string. No separator found.\");\n  }\n  const prefix = line.slice(0, firstSeparatorIndex);\n  if (!validCodes.includes(prefix)) {\n    throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);\n  }\n  const code = prefix;\n  const textValue = line.slice(firstSeparatorIndex + 1);\n  const jsonValue = JSON.parse(textValue);\n  return assistantStreamPartsByCode[code].parse(jsonValue);\n};\nfunction formatAssistantStreamPart(type, value) {\n  const streamPart = assistantStreamParts.find((part) => part.name === type);\n  if (!streamPart) {\n    throw new Error(`Invalid stream part type: ${type}`);\n  }\n  return `${streamPart.code}:${JSON.stringify(value)}\n`;\n}\n\n// src/process-chat-response.ts\n\n\n// src/duplicated/usage.ts\nfunction calculateLanguageModelUsage({\n  promptTokens,\n  completionTokens\n}) {\n  return {\n    promptTokens,\n    completionTokens,\n    totalTokens: promptTokens + completionTokens\n  };\n}\n\n// src/parse-partial-json.ts\n\n\n// src/fix-json.ts\nfunction fixJson(input) {\n  const stack = [\"ROOT\"];\n  let lastValidIndex = -1;\n  let literalStart = null;\n  function processValueStart(char, i, swapState) {\n    {\n      switch (char) {\n        case '\"': {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_STRING\");\n          break;\n        }\n        case \"f\":\n        case \"t\":\n        case \"n\": {\n          lastValidIndex = i;\n          literalStart = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_LITERAL\");\n          break;\n        }\n        case \"-\": {\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_NUMBER\");\n          break;\n        }\n        case \"0\":\n        case \"1\":\n        case \"2\":\n        case \"3\":\n        case \"4\":\n        case \"5\":\n        case \"6\":\n        case \"7\":\n        case \"8\":\n        case \"9\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_NUMBER\");\n          break;\n        }\n        case \"{\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_OBJECT_START\");\n          break;\n        }\n        case \"[\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_ARRAY_START\");\n          break;\n        }\n      }\n    }\n  }\n  function processAfterObjectValue(char, i) {\n    switch (char) {\n      case \",\": {\n        stack.pop();\n        stack.push(\"INSIDE_OBJECT_AFTER_COMMA\");\n        break;\n      }\n      case \"}\": {\n        lastValidIndex = i;\n        stack.pop();\n        break;\n      }\n    }\n  }\n  function processAfterArrayValue(char, i) {\n    switch (char) {\n      case \",\": {\n        stack.pop();\n        stack.push(\"INSIDE_ARRAY_AFTER_COMMA\");\n        break;\n      }\n      case \"]\": {\n        lastValidIndex = i;\n        stack.pop();\n        break;\n      }\n    }\n  }\n  for (let i = 0; i < input.length; i++) {\n    const char = input[i];\n    const currentState = stack[stack.length - 1];\n    switch (currentState) {\n      case \"ROOT\":\n        processValueStart(char, i, \"FINISH\");\n        break;\n      case \"INSIDE_OBJECT_START\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_KEY\");\n            break;\n          }\n          case \"}\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_COMMA\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_KEY\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_KEY\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_AFTER_KEY\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_KEY\": {\n        switch (char) {\n          case \":\": {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_BEFORE_VALUE\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_BEFORE_VALUE\": {\n        processValueStart(char, i, \"INSIDE_OBJECT_AFTER_VALUE\");\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_VALUE\": {\n        processAfterObjectValue(char, i);\n        break;\n      }\n      case \"INSIDE_STRING\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            lastValidIndex = i;\n            break;\n          }\n          case \"\\\\\": {\n            stack.push(\"INSIDE_STRING_ESCAPE\");\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_START\": {\n        switch (char) {\n          case \"]\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n            processValueStart(char, i, \"INSIDE_ARRAY_AFTER_VALUE\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_AFTER_VALUE\": {\n        switch (char) {\n          case \",\": {\n            stack.pop();\n            stack.push(\"INSIDE_ARRAY_AFTER_COMMA\");\n            break;\n          }\n          case \"]\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_AFTER_COMMA\": {\n        processValueStart(char, i, \"INSIDE_ARRAY_AFTER_VALUE\");\n        break;\n      }\n      case \"INSIDE_STRING_ESCAPE\": {\n        stack.pop();\n        lastValidIndex = i;\n        break;\n      }\n      case \"INSIDE_NUMBER\": {\n        switch (char) {\n          case \"0\":\n          case \"1\":\n          case \"2\":\n          case \"3\":\n          case \"4\":\n          case \"5\":\n          case \"6\":\n          case \"7\":\n          case \"8\":\n          case \"9\": {\n            lastValidIndex = i;\n            break;\n          }\n          case \"e\":\n          case \"E\":\n          case \"-\":\n          case \".\": {\n            break;\n          }\n          case \",\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n              processAfterArrayValue(char, i);\n            }\n            if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n              processAfterObjectValue(char, i);\n            }\n            break;\n          }\n          case \"}\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n              processAfterObjectValue(char, i);\n            }\n            break;\n          }\n          case \"]\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n              processAfterArrayValue(char, i);\n            }\n            break;\n          }\n          default: {\n            stack.pop();\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_LITERAL\": {\n        const partialLiteral = input.substring(literalStart, i + 1);\n        if (!\"false\".startsWith(partialLiteral) && !\"true\".startsWith(partialLiteral) && !\"null\".startsWith(partialLiteral)) {\n          stack.pop();\n          if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n            processAfterObjectValue(char, i);\n          } else if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n            processAfterArrayValue(char, i);\n          }\n        } else {\n          lastValidIndex = i;\n        }\n        break;\n      }\n    }\n  }\n  let result = input.slice(0, lastValidIndex + 1);\n  for (let i = stack.length - 1; i >= 0; i--) {\n    const state = stack[i];\n    switch (state) {\n      case \"INSIDE_STRING\": {\n        result += '\"';\n        break;\n      }\n      case \"INSIDE_OBJECT_KEY\":\n      case \"INSIDE_OBJECT_AFTER_KEY\":\n      case \"INSIDE_OBJECT_AFTER_COMMA\":\n      case \"INSIDE_OBJECT_START\":\n      case \"INSIDE_OBJECT_BEFORE_VALUE\":\n      case \"INSIDE_OBJECT_AFTER_VALUE\": {\n        result += \"}\";\n        break;\n      }\n      case \"INSIDE_ARRAY_START\":\n      case \"INSIDE_ARRAY_AFTER_COMMA\":\n      case \"INSIDE_ARRAY_AFTER_VALUE\": {\n        result += \"]\";\n        break;\n      }\n      case \"INSIDE_LITERAL\": {\n        const partialLiteral = input.substring(literalStart, input.length);\n        if (\"true\".startsWith(partialLiteral)) {\n          result += \"true\".slice(partialLiteral.length);\n        } else if (\"false\".startsWith(partialLiteral)) {\n          result += \"false\".slice(partialLiteral.length);\n        } else if (\"null\".startsWith(partialLiteral)) {\n          result += \"null\".slice(partialLiteral.length);\n        }\n      }\n    }\n  }\n  return result;\n}\n\n// src/parse-partial-json.ts\nfunction parsePartialJson(jsonText) {\n  if (jsonText === void 0) {\n    return { value: void 0, state: \"undefined-input\" };\n  }\n  let result = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.safeParseJSON)({ text: jsonText });\n  if (result.success) {\n    return { value: result.value, state: \"successful-parse\" };\n  }\n  result = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.safeParseJSON)({ text: fixJson(jsonText) });\n  if (result.success) {\n    return { value: result.value, state: \"repaired-parse\" };\n  }\n  return { value: void 0, state: \"failed-parse\" };\n}\n\n// src/data-stream-parts.ts\nvar textStreamPart2 = {\n  code: \"0\",\n  name: \"text\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"text\" parts expect a string value.');\n    }\n    return { type: \"text\", value };\n  }\n};\nvar dataStreamPart = {\n  code: \"2\",\n  name: \"data\",\n  parse: (value) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"data\" parts expect an array value.');\n    }\n    return { type: \"data\", value };\n  }\n};\nvar errorStreamPart2 = {\n  code: \"3\",\n  name: \"error\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"error\" parts expect a string value.');\n    }\n    return { type: \"error\", value };\n  }\n};\nvar messageAnnotationsStreamPart = {\n  code: \"8\",\n  name: \"message_annotations\",\n  parse: (value) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"message_annotations\" parts expect an array value.');\n    }\n    return { type: \"message_annotations\", value };\n  }\n};\nvar toolCallStreamPart = {\n  code: \"9\",\n  name: \"tool_call\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"toolName\" in value) || typeof value.toolName !== \"string\" || !(\"args\" in value) || typeof value.args !== \"object\") {\n      throw new Error(\n        '\"tool_call\" parts expect an object with a \"toolCallId\", \"toolName\", and \"args\" property.'\n      );\n    }\n    return {\n      type: \"tool_call\",\n      value\n    };\n  }\n};\nvar toolResultStreamPart = {\n  code: \"a\",\n  name: \"tool_result\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"result\" in value)) {\n      throw new Error(\n        '\"tool_result\" parts expect an object with a \"toolCallId\" and a \"result\" property.'\n      );\n    }\n    return {\n      type: \"tool_result\",\n      value\n    };\n  }\n};\nvar toolCallStreamingStartStreamPart = {\n  code: \"b\",\n  name: \"tool_call_streaming_start\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"toolName\" in value) || typeof value.toolName !== \"string\") {\n      throw new Error(\n        '\"tool_call_streaming_start\" parts expect an object with a \"toolCallId\" and \"toolName\" property.'\n      );\n    }\n    return {\n      type: \"tool_call_streaming_start\",\n      value\n    };\n  }\n};\nvar toolCallDeltaStreamPart = {\n  code: \"c\",\n  name: \"tool_call_delta\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"argsTextDelta\" in value) || typeof value.argsTextDelta !== \"string\") {\n      throw new Error(\n        '\"tool_call_delta\" parts expect an object with a \"toolCallId\" and \"argsTextDelta\" property.'\n      );\n    }\n    return {\n      type: \"tool_call_delta\",\n      value\n    };\n  }\n};\nvar finishMessageStreamPart = {\n  code: \"d\",\n  name: \"finish_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"finishReason\" in value) || typeof value.finishReason !== \"string\") {\n      throw new Error(\n        '\"finish_message\" parts expect an object with a \"finishReason\" property.'\n      );\n    }\n    const result = {\n      finishReason: value.finishReason\n    };\n    if (\"usage\" in value && value.usage != null && typeof value.usage === \"object\" && \"promptTokens\" in value.usage && \"completionTokens\" in value.usage) {\n      result.usage = {\n        promptTokens: typeof value.usage.promptTokens === \"number\" ? value.usage.promptTokens : Number.NaN,\n        completionTokens: typeof value.usage.completionTokens === \"number\" ? value.usage.completionTokens : Number.NaN\n      };\n    }\n    return {\n      type: \"finish_message\",\n      value: result\n    };\n  }\n};\nvar finishStepStreamPart = {\n  code: \"e\",\n  name: \"finish_step\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"finishReason\" in value) || typeof value.finishReason !== \"string\") {\n      throw new Error(\n        '\"finish_step\" parts expect an object with a \"finishReason\" property.'\n      );\n    }\n    const result = {\n      finishReason: value.finishReason,\n      isContinued: false\n    };\n    if (\"usage\" in value && value.usage != null && typeof value.usage === \"object\" && \"promptTokens\" in value.usage && \"completionTokens\" in value.usage) {\n      result.usage = {\n        promptTokens: typeof value.usage.promptTokens === \"number\" ? value.usage.promptTokens : Number.NaN,\n        completionTokens: typeof value.usage.completionTokens === \"number\" ? value.usage.completionTokens : Number.NaN\n      };\n    }\n    if (\"isContinued\" in value && typeof value.isContinued === \"boolean\") {\n      result.isContinued = value.isContinued;\n    }\n    return {\n      type: \"finish_step\",\n      value: result\n    };\n  }\n};\nvar startStepStreamPart = {\n  code: \"f\",\n  name: \"start_step\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"messageId\" in value) || typeof value.messageId !== \"string\") {\n      throw new Error(\n        '\"start_step\" parts expect an object with an \"id\" property.'\n      );\n    }\n    return {\n      type: \"start_step\",\n      value: {\n        messageId: value.messageId\n      }\n    };\n  }\n};\nvar reasoningStreamPart = {\n  code: \"g\",\n  name: \"reasoning\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"reasoning\" parts expect a string value.');\n    }\n    return { type: \"reasoning\", value };\n  }\n};\nvar sourcePart = {\n  code: \"h\",\n  name: \"source\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\") {\n      throw new Error('\"source\" parts expect a Source object.');\n    }\n    return {\n      type: \"source\",\n      value\n    };\n  }\n};\nvar redactedReasoningStreamPart = {\n  code: \"i\",\n  name: \"redacted_reasoning\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"data\" in value) || typeof value.data !== \"string\") {\n      throw new Error(\n        '\"redacted_reasoning\" parts expect an object with a \"data\" property.'\n      );\n    }\n    return { type: \"redacted_reasoning\", value: { data: value.data } };\n  }\n};\nvar reasoningSignatureStreamPart = {\n  code: \"j\",\n  name: \"reasoning_signature\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"signature\" in value) || typeof value.signature !== \"string\") {\n      throw new Error(\n        '\"reasoning_signature\" parts expect an object with a \"signature\" property.'\n      );\n    }\n    return {\n      type: \"reasoning_signature\",\n      value: { signature: value.signature }\n    };\n  }\n};\nvar fileStreamPart = {\n  code: \"k\",\n  name: \"file\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"data\" in value) || typeof value.data !== \"string\" || !(\"mimeType\" in value) || typeof value.mimeType !== \"string\") {\n      throw new Error(\n        '\"file\" parts expect an object with a \"data\" and \"mimeType\" property.'\n      );\n    }\n    return { type: \"file\", value };\n  }\n};\nvar dataStreamParts = [\n  textStreamPart2,\n  dataStreamPart,\n  errorStreamPart2,\n  messageAnnotationsStreamPart,\n  toolCallStreamPart,\n  toolResultStreamPart,\n  toolCallStreamingStartStreamPart,\n  toolCallDeltaStreamPart,\n  finishMessageStreamPart,\n  finishStepStreamPart,\n  startStepStreamPart,\n  reasoningStreamPart,\n  sourcePart,\n  redactedReasoningStreamPart,\n  reasoningSignatureStreamPart,\n  fileStreamPart\n];\nvar dataStreamPartsByCode = Object.fromEntries(\n  dataStreamParts.map((part) => [part.code, part])\n);\nvar DataStreamStringPrefixes = Object.fromEntries(\n  dataStreamParts.map((part) => [part.name, part.code])\n);\nvar validCodes2 = dataStreamParts.map((part) => part.code);\nvar parseDataStreamPart = (line) => {\n  const firstSeparatorIndex = line.indexOf(\":\");\n  if (firstSeparatorIndex === -1) {\n    throw new Error(\"Failed to parse stream string. No separator found.\");\n  }\n  const prefix = line.slice(0, firstSeparatorIndex);\n  if (!validCodes2.includes(prefix)) {\n    throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);\n  }\n  const code = prefix;\n  const textValue = line.slice(firstSeparatorIndex + 1);\n  const jsonValue = JSON.parse(textValue);\n  return dataStreamPartsByCode[code].parse(jsonValue);\n};\nfunction formatDataStreamPart(type, value) {\n  const streamPart = dataStreamParts.find((part) => part.name === type);\n  if (!streamPart) {\n    throw new Error(`Invalid stream part type: ${type}`);\n  }\n  return `${streamPart.code}:${JSON.stringify(value)}\n`;\n}\n\n// src/process-data-stream.ts\nvar NEWLINE = \"\\n\".charCodeAt(0);\nfunction concatChunks(chunks, totalLength) {\n  const concatenatedChunks = new Uint8Array(totalLength);\n  let offset = 0;\n  for (const chunk of chunks) {\n    concatenatedChunks.set(chunk, offset);\n    offset += chunk.length;\n  }\n  chunks.length = 0;\n  return concatenatedChunks;\n}\nasync function processDataStream({\n  stream,\n  onTextPart,\n  onReasoningPart,\n  onReasoningSignaturePart,\n  onRedactedReasoningPart,\n  onSourcePart,\n  onFilePart,\n  onDataPart,\n  onErrorPart,\n  onToolCallStreamingStartPart,\n  onToolCallDeltaPart,\n  onToolCallPart,\n  onToolResultPart,\n  onMessageAnnotationsPart,\n  onFinishMessagePart,\n  onFinishStepPart,\n  onStartStepPart\n}) {\n  const reader = stream.getReader();\n  const decoder = new TextDecoder();\n  const chunks = [];\n  let totalLength = 0;\n  while (true) {\n    const { value } = await reader.read();\n    if (value) {\n      chunks.push(value);\n      totalLength += value.length;\n      if (value[value.length - 1] !== NEWLINE) {\n        continue;\n      }\n    }\n    if (chunks.length === 0) {\n      break;\n    }\n    const concatenatedChunks = concatChunks(chunks, totalLength);\n    totalLength = 0;\n    const streamParts = decoder.decode(concatenatedChunks, { stream: true }).split(\"\\n\").filter((line) => line !== \"\").map(parseDataStreamPart);\n    for (const { type, value: value2 } of streamParts) {\n      switch (type) {\n        case \"text\":\n          await (onTextPart == null ? void 0 : onTextPart(value2));\n          break;\n        case \"reasoning\":\n          await (onReasoningPart == null ? void 0 : onReasoningPart(value2));\n          break;\n        case \"reasoning_signature\":\n          await (onReasoningSignaturePart == null ? void 0 : onReasoningSignaturePart(value2));\n          break;\n        case \"redacted_reasoning\":\n          await (onRedactedReasoningPart == null ? void 0 : onRedactedReasoningPart(value2));\n          break;\n        case \"file\":\n          await (onFilePart == null ? void 0 : onFilePart(value2));\n          break;\n        case \"source\":\n          await (onSourcePart == null ? void 0 : onSourcePart(value2));\n          break;\n        case \"data\":\n          await (onDataPart == null ? void 0 : onDataPart(value2));\n          break;\n        case \"error\":\n          await (onErrorPart == null ? void 0 : onErrorPart(value2));\n          break;\n        case \"message_annotations\":\n          await (onMessageAnnotationsPart == null ? void 0 : onMessageAnnotationsPart(value2));\n          break;\n        case \"tool_call_streaming_start\":\n          await (onToolCallStreamingStartPart == null ? void 0 : onToolCallStreamingStartPart(value2));\n          break;\n        case \"tool_call_delta\":\n          await (onToolCallDeltaPart == null ? void 0 : onToolCallDeltaPart(value2));\n          break;\n        case \"tool_call\":\n          await (onToolCallPart == null ? void 0 : onToolCallPart(value2));\n          break;\n        case \"tool_result\":\n          await (onToolResultPart == null ? void 0 : onToolResultPart(value2));\n          break;\n        case \"finish_message\":\n          await (onFinishMessagePart == null ? void 0 : onFinishMessagePart(value2));\n          break;\n        case \"finish_step\":\n          await (onFinishStepPart == null ? void 0 : onFinishStepPart(value2));\n          break;\n        case \"start_step\":\n          await (onStartStepPart == null ? void 0 : onStartStepPart(value2));\n          break;\n        default: {\n          const exhaustiveCheck = type;\n          throw new Error(`Unknown stream part type: ${exhaustiveCheck}`);\n        }\n      }\n    }\n  }\n}\n\n// src/process-chat-response.ts\nasync function processChatResponse({\n  stream,\n  update,\n  onToolCall,\n  onFinish,\n  generateId: generateId2 = _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId,\n  getCurrentDate = () => /* @__PURE__ */ new Date(),\n  lastMessage\n}) {\n  var _a, _b;\n  const replaceLastMessage = (lastMessage == null ? void 0 : lastMessage.role) === \"assistant\";\n  let step = replaceLastMessage ? 1 + // find max step in existing tool invocations:\n  ((_b = (_a = lastMessage.toolInvocations) == null ? void 0 : _a.reduce((max, toolInvocation) => {\n    var _a2;\n    return Math.max(max, (_a2 = toolInvocation.step) != null ? _a2 : 0);\n  }, 0)) != null ? _b : 0) : 0;\n  const message = replaceLastMessage ? structuredClone(lastMessage) : {\n    id: generateId2(),\n    createdAt: getCurrentDate(),\n    role: \"assistant\",\n    content: \"\",\n    parts: []\n  };\n  let currentTextPart = void 0;\n  let currentReasoningPart = void 0;\n  let currentReasoningTextDetail = void 0;\n  function updateToolInvocationPart(toolCallId, invocation) {\n    const part = message.parts.find(\n      (part2) => part2.type === \"tool-invocation\" && part2.toolInvocation.toolCallId === toolCallId\n    );\n    if (part != null) {\n      part.toolInvocation = invocation;\n    } else {\n      message.parts.push({\n        type: \"tool-invocation\",\n        toolInvocation: invocation\n      });\n    }\n  }\n  const data = [];\n  let messageAnnotations = replaceLastMessage ? lastMessage == null ? void 0 : lastMessage.annotations : void 0;\n  const partialToolCalls = {};\n  let usage = {\n    completionTokens: NaN,\n    promptTokens: NaN,\n    totalTokens: NaN\n  };\n  let finishReason = \"unknown\";\n  function execUpdate() {\n    const copiedData = [...data];\n    if (messageAnnotations == null ? void 0 : messageAnnotations.length) {\n      message.annotations = messageAnnotations;\n    }\n    const copiedMessage = {\n      // deep copy the message to ensure that deep changes (msg attachments) are updated\n      // with SolidJS. SolidJS uses referential integration of sub-objects to detect changes.\n      ...structuredClone(message),\n      // add a revision id to ensure that the message is updated with SWR. SWR uses a\n      // hashing approach by default to detect changes, but it only works for shallow\n      // changes. This is why we need to add a revision id to ensure that the message\n      // is updated with SWR (without it, the changes get stuck in SWR and are not\n      // forwarded to rendering):\n      revisionId: generateId2()\n    };\n    update({\n      message: copiedMessage,\n      data: copiedData,\n      replaceLastMessage\n    });\n  }\n  await processDataStream({\n    stream,\n    onTextPart(value) {\n      if (currentTextPart == null) {\n        currentTextPart = {\n          type: \"text\",\n          text: value\n        };\n        message.parts.push(currentTextPart);\n      } else {\n        currentTextPart.text += value;\n      }\n      message.content += value;\n      execUpdate();\n    },\n    onReasoningPart(value) {\n      var _a2;\n      if (currentReasoningTextDetail == null) {\n        currentReasoningTextDetail = { type: \"text\", text: value };\n        if (currentReasoningPart != null) {\n          currentReasoningPart.details.push(currentReasoningTextDetail);\n        }\n      } else {\n        currentReasoningTextDetail.text += value;\n      }\n      if (currentReasoningPart == null) {\n        currentReasoningPart = {\n          type: \"reasoning\",\n          reasoning: value,\n          details: [currentReasoningTextDetail]\n        };\n        message.parts.push(currentReasoningPart);\n      } else {\n        currentReasoningPart.reasoning += value;\n      }\n      message.reasoning = ((_a2 = message.reasoning) != null ? _a2 : \"\") + value;\n      execUpdate();\n    },\n    onReasoningSignaturePart(value) {\n      if (currentReasoningTextDetail != null) {\n        currentReasoningTextDetail.signature = value.signature;\n      }\n    },\n    onRedactedReasoningPart(value) {\n      if (currentReasoningPart == null) {\n        currentReasoningPart = {\n          type: \"reasoning\",\n          reasoning: \"\",\n          details: []\n        };\n        message.parts.push(currentReasoningPart);\n      }\n      currentReasoningPart.details.push({\n        type: \"redacted\",\n        data: value.data\n      });\n      currentReasoningTextDetail = void 0;\n      execUpdate();\n    },\n    onFilePart(value) {\n      message.parts.push({\n        type: \"file\",\n        mimeType: value.mimeType,\n        data: value.data\n      });\n      execUpdate();\n    },\n    onSourcePart(value) {\n      message.parts.push({\n        type: \"source\",\n        source: value\n      });\n      execUpdate();\n    },\n    onToolCallStreamingStartPart(value) {\n      if (message.toolInvocations == null) {\n        message.toolInvocations = [];\n      }\n      partialToolCalls[value.toolCallId] = {\n        text: \"\",\n        step,\n        toolName: value.toolName,\n        index: message.toolInvocations.length\n      };\n      const invocation = {\n        state: \"partial-call\",\n        step,\n        toolCallId: value.toolCallId,\n        toolName: value.toolName,\n        args: void 0\n      };\n      message.toolInvocations.push(invocation);\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n    },\n    onToolCallDeltaPart(value) {\n      const partialToolCall = partialToolCalls[value.toolCallId];\n      partialToolCall.text += value.argsTextDelta;\n      const { value: partialArgs } = parsePartialJson(partialToolCall.text);\n      const invocation = {\n        state: \"partial-call\",\n        step: partialToolCall.step,\n        toolCallId: value.toolCallId,\n        toolName: partialToolCall.toolName,\n        args: partialArgs\n      };\n      message.toolInvocations[partialToolCall.index] = invocation;\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n    },\n    async onToolCallPart(value) {\n      const invocation = {\n        state: \"call\",\n        step,\n        ...value\n      };\n      if (partialToolCalls[value.toolCallId] != null) {\n        message.toolInvocations[partialToolCalls[value.toolCallId].index] = invocation;\n      } else {\n        if (message.toolInvocations == null) {\n          message.toolInvocations = [];\n        }\n        message.toolInvocations.push(invocation);\n      }\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n      if (onToolCall) {\n        const result = await onToolCall({ toolCall: value });\n        if (result != null) {\n          const invocation2 = {\n            state: \"result\",\n            step,\n            ...value,\n            result\n          };\n          message.toolInvocations[message.toolInvocations.length - 1] = invocation2;\n          updateToolInvocationPart(value.toolCallId, invocation2);\n          execUpdate();\n        }\n      }\n    },\n    onToolResultPart(value) {\n      const toolInvocations = message.toolInvocations;\n      if (toolInvocations == null) {\n        throw new Error(\"tool_result must be preceded by a tool_call\");\n      }\n      const toolInvocationIndex = toolInvocations.findIndex(\n        (invocation2) => invocation2.toolCallId === value.toolCallId\n      );\n      if (toolInvocationIndex === -1) {\n        throw new Error(\n          \"tool_result must be preceded by a tool_call with the same toolCallId\"\n        );\n      }\n      const invocation = {\n        ...toolInvocations[toolInvocationIndex],\n        state: \"result\",\n        ...value\n      };\n      toolInvocations[toolInvocationIndex] = invocation;\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n    },\n    onDataPart(value) {\n      data.push(...value);\n      execUpdate();\n    },\n    onMessageAnnotationsPart(value) {\n      if (messageAnnotations == null) {\n        messageAnnotations = [...value];\n      } else {\n        messageAnnotations.push(...value);\n      }\n      execUpdate();\n    },\n    onFinishStepPart(value) {\n      step += 1;\n      currentTextPart = value.isContinued ? currentTextPart : void 0;\n      currentReasoningPart = void 0;\n      currentReasoningTextDetail = void 0;\n    },\n    onStartStepPart(value) {\n      if (!replaceLastMessage) {\n        message.id = value.messageId;\n      }\n      message.parts.push({ type: \"step-start\" });\n      execUpdate();\n    },\n    onFinishMessagePart(value) {\n      finishReason = value.finishReason;\n      if (value.usage != null) {\n        usage = calculateLanguageModelUsage(value.usage);\n      }\n    },\n    onErrorPart(error) {\n      throw new Error(error);\n    }\n  });\n  onFinish == null ? void 0 : onFinish({ message, finishReason, usage });\n}\n\n// src/process-chat-text-response.ts\n\n\n// src/process-text-stream.ts\nasync function processTextStream({\n  stream,\n  onTextPart\n}) {\n  const reader = stream.pipeThrough(new TextDecoderStream()).getReader();\n  while (true) {\n    const { done, value } = await reader.read();\n    if (done) {\n      break;\n    }\n    await onTextPart(value);\n  }\n}\n\n// src/process-chat-text-response.ts\nasync function processChatTextResponse({\n  stream,\n  update,\n  onFinish,\n  getCurrentDate = () => /* @__PURE__ */ new Date(),\n  generateId: generateId2 = _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId\n}) {\n  const textPart = { type: \"text\", text: \"\" };\n  const resultMessage = {\n    id: generateId2(),\n    createdAt: getCurrentDate(),\n    role: \"assistant\",\n    content: \"\",\n    parts: [textPart]\n  };\n  await processTextStream({\n    stream,\n    onTextPart: (chunk) => {\n      resultMessage.content += chunk;\n      textPart.text += chunk;\n      update({\n        message: { ...resultMessage },\n        data: [],\n        replaceLastMessage: false\n      });\n    }\n  });\n  onFinish == null ? void 0 : onFinish(resultMessage, {\n    usage: { completionTokens: NaN, promptTokens: NaN, totalTokens: NaN },\n    finishReason: \"unknown\"\n  });\n}\n\n// src/call-chat-api.ts\nvar getOriginalFetch = () => fetch;\nasync function callChatApi({\n  api,\n  body,\n  streamProtocol = \"data\",\n  credentials,\n  headers,\n  abortController,\n  restoreMessagesOnFailure,\n  onResponse,\n  onUpdate,\n  onFinish,\n  onToolCall,\n  generateId: generateId2,\n  fetch: fetch2 = getOriginalFetch(),\n  lastMessage,\n  requestType = \"generate\"\n}) {\n  var _a, _b, _c;\n  const request = requestType === \"resume\" ? fetch2(`${api}?chatId=${body.id}`, {\n    method: \"GET\",\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...headers\n    },\n    signal: (_a = abortController == null ? void 0 : abortController()) == null ? void 0 : _a.signal,\n    credentials\n  }) : fetch2(api, {\n    method: \"POST\",\n    body: JSON.stringify(body),\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...headers\n    },\n    signal: (_b = abortController == null ? void 0 : abortController()) == null ? void 0 : _b.signal,\n    credentials\n  });\n  const response = await request.catch((err) => {\n    restoreMessagesOnFailure();\n    throw err;\n  });\n  if (onResponse) {\n    try {\n      await onResponse(response);\n    } catch (err) {\n      throw err;\n    }\n  }\n  if (!response.ok) {\n    restoreMessagesOnFailure();\n    throw new Error(\n      (_c = await response.text()) != null ? _c : \"Failed to fetch the chat response.\"\n    );\n  }\n  if (!response.body) {\n    throw new Error(\"The response body is empty.\");\n  }\n  switch (streamProtocol) {\n    case \"text\": {\n      await processChatTextResponse({\n        stream: response.body,\n        update: onUpdate,\n        onFinish,\n        generateId: generateId2\n      });\n      return;\n    }\n    case \"data\": {\n      await processChatResponse({\n        stream: response.body,\n        update: onUpdate,\n        lastMessage,\n        onToolCall,\n        onFinish({ message, finishReason, usage }) {\n          if (onFinish && message != null) {\n            onFinish(message, { usage, finishReason });\n          }\n        },\n        generateId: generateId2\n      });\n      return;\n    }\n    default: {\n      const exhaustiveCheck = streamProtocol;\n      throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);\n    }\n  }\n}\n\n// src/call-completion-api.ts\nvar getOriginalFetch2 = () => fetch;\nasync function callCompletionApi({\n  api,\n  prompt,\n  credentials,\n  headers,\n  body,\n  streamProtocol = \"data\",\n  setCompletion,\n  setLoading,\n  setError,\n  setAbortController,\n  onResponse,\n  onFinish,\n  onError,\n  onData,\n  fetch: fetch2 = getOriginalFetch2()\n}) {\n  var _a;\n  try {\n    setLoading(true);\n    setError(void 0);\n    const abortController = new AbortController();\n    setAbortController(abortController);\n    setCompletion(\"\");\n    const response = await fetch2(api, {\n      method: \"POST\",\n      body: JSON.stringify({\n        prompt,\n        ...body\n      }),\n      credentials,\n      headers: {\n        \"Content-Type\": \"application/json\",\n        ...headers\n      },\n      signal: abortController.signal\n    }).catch((err) => {\n      throw err;\n    });\n    if (onResponse) {\n      try {\n        await onResponse(response);\n      } catch (err) {\n        throw err;\n      }\n    }\n    if (!response.ok) {\n      throw new Error(\n        (_a = await response.text()) != null ? _a : \"Failed to fetch the chat response.\"\n      );\n    }\n    if (!response.body) {\n      throw new Error(\"The response body is empty.\");\n    }\n    let result = \"\";\n    switch (streamProtocol) {\n      case \"text\": {\n        await processTextStream({\n          stream: response.body,\n          onTextPart: (chunk) => {\n            result += chunk;\n            setCompletion(result);\n          }\n        });\n        break;\n      }\n      case \"data\": {\n        await processDataStream({\n          stream: response.body,\n          onTextPart(value) {\n            result += value;\n            setCompletion(result);\n          },\n          onDataPart(value) {\n            onData == null ? void 0 : onData(value);\n          },\n          onErrorPart(value) {\n            throw new Error(value);\n          }\n        });\n        break;\n      }\n      default: {\n        const exhaustiveCheck = streamProtocol;\n        throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);\n      }\n    }\n    if (onFinish) {\n      onFinish(prompt, result);\n    }\n    setAbortController(null);\n    return result;\n  } catch (err) {\n    if (err.name === \"AbortError\") {\n      setAbortController(null);\n      return null;\n    }\n    if (err instanceof Error) {\n      if (onError) {\n        onError(err);\n      }\n    }\n    setError(err);\n  } finally {\n    setLoading(false);\n  }\n}\n\n// src/data-url.ts\nfunction getTextFromDataUrl(dataUrl) {\n  const [header, base64Content] = dataUrl.split(\",\");\n  const mimeType = header.split(\";\")[0].split(\":\")[1];\n  if (mimeType == null || base64Content == null) {\n    throw new Error(\"Invalid data URL format\");\n  }\n  try {\n    return window.atob(base64Content);\n  } catch (error) {\n    throw new Error(`Error decoding data URL`);\n  }\n}\n\n// src/extract-max-tool-invocation-step.ts\nfunction extractMaxToolInvocationStep(toolInvocations) {\n  return toolInvocations == null ? void 0 : toolInvocations.reduce((max, toolInvocation) => {\n    var _a;\n    return Math.max(max, (_a = toolInvocation.step) != null ? _a : 0);\n  }, 0);\n}\n\n// src/get-message-parts.ts\nfunction getMessageParts(message) {\n  var _a;\n  return (_a = message.parts) != null ? _a : [\n    ...message.toolInvocations ? message.toolInvocations.map((toolInvocation) => ({\n      type: \"tool-invocation\",\n      toolInvocation\n    })) : [],\n    ...message.reasoning ? [\n      {\n        type: \"reasoning\",\n        reasoning: message.reasoning,\n        details: [{ type: \"text\", text: message.reasoning }]\n      }\n    ] : [],\n    ...message.content ? [{ type: \"text\", text: message.content }] : []\n  ];\n}\n\n// src/fill-message-parts.ts\nfunction fillMessageParts(messages) {\n  return messages.map((message) => ({\n    ...message,\n    parts: getMessageParts(message)\n  }));\n}\n\n// src/is-deep-equal-data.ts\nfunction isDeepEqualData(obj1, obj2) {\n  if (obj1 === obj2)\n    return true;\n  if (obj1 == null || obj2 == null)\n    return false;\n  if (typeof obj1 !== \"object\" && typeof obj2 !== \"object\")\n    return obj1 === obj2;\n  if (obj1.constructor !== obj2.constructor)\n    return false;\n  if (obj1 instanceof Date && obj2 instanceof Date) {\n    return obj1.getTime() === obj2.getTime();\n  }\n  if (Array.isArray(obj1)) {\n    if (obj1.length !== obj2.length)\n      return false;\n    for (let i = 0; i < obj1.length; i++) {\n      if (!isDeepEqualData(obj1[i], obj2[i]))\n        return false;\n    }\n    return true;\n  }\n  const keys1 = Object.keys(obj1);\n  const keys2 = Object.keys(obj2);\n  if (keys1.length !== keys2.length)\n    return false;\n  for (const key of keys1) {\n    if (!keys2.includes(key))\n      return false;\n    if (!isDeepEqualData(obj1[key], obj2[key]))\n      return false;\n  }\n  return true;\n}\n\n// src/prepare-attachments-for-request.ts\nasync function prepareAttachmentsForRequest(attachmentsFromOptions) {\n  if (!attachmentsFromOptions) {\n    return [];\n  }\n  if (globalThis.FileList && attachmentsFromOptions instanceof globalThis.FileList) {\n    return Promise.all(\n      Array.from(attachmentsFromOptions).map(async (attachment) => {\n        const { name, type } = attachment;\n        const dataUrl = await new Promise((resolve, reject) => {\n          const reader = new FileReader();\n          reader.onload = (readerEvent) => {\n            var _a;\n            resolve((_a = readerEvent.target) == null ? void 0 : _a.result);\n          };\n          reader.onerror = (error) => reject(error);\n          reader.readAsDataURL(attachment);\n        });\n        return {\n          name,\n          contentType: type,\n          url: dataUrl\n        };\n      })\n    );\n  }\n  if (Array.isArray(attachmentsFromOptions)) {\n    return attachmentsFromOptions;\n  }\n  throw new Error(\"Invalid attachments type\");\n}\n\n// src/process-assistant-stream.ts\nvar NEWLINE2 = \"\\n\".charCodeAt(0);\nfunction concatChunks2(chunks, totalLength) {\n  const concatenatedChunks = new Uint8Array(totalLength);\n  let offset = 0;\n  for (const chunk of chunks) {\n    concatenatedChunks.set(chunk, offset);\n    offset += chunk.length;\n  }\n  chunks.length = 0;\n  return concatenatedChunks;\n}\nasync function processAssistantStream({\n  stream,\n  onTextPart,\n  onErrorPart,\n  onAssistantMessagePart,\n  onAssistantControlDataPart,\n  onDataMessagePart\n}) {\n  const reader = stream.getReader();\n  const decoder = new TextDecoder();\n  const chunks = [];\n  let totalLength = 0;\n  while (true) {\n    const { value } = await reader.read();\n    if (value) {\n      chunks.push(value);\n      totalLength += value.length;\n      if (value[value.length - 1] !== NEWLINE2) {\n        continue;\n      }\n    }\n    if (chunks.length === 0) {\n      break;\n    }\n    const concatenatedChunks = concatChunks2(chunks, totalLength);\n    totalLength = 0;\n    const streamParts = decoder.decode(concatenatedChunks, { stream: true }).split(\"\\n\").filter((line) => line !== \"\").map(parseAssistantStreamPart);\n    for (const { type, value: value2 } of streamParts) {\n      switch (type) {\n        case \"text\":\n          await (onTextPart == null ? void 0 : onTextPart(value2));\n          break;\n        case \"error\":\n          await (onErrorPart == null ? void 0 : onErrorPart(value2));\n          break;\n        case \"assistant_message\":\n          await (onAssistantMessagePart == null ? void 0 : onAssistantMessagePart(value2));\n          break;\n        case \"assistant_control_data\":\n          await (onAssistantControlDataPart == null ? void 0 : onAssistantControlDataPart(value2));\n          break;\n        case \"data_message\":\n          await (onDataMessagePart == null ? void 0 : onDataMessagePart(value2));\n          break;\n        default: {\n          const exhaustiveCheck = type;\n          throw new Error(`Unknown stream part type: ${exhaustiveCheck}`);\n        }\n      }\n    }\n  }\n}\n\n// src/schema.ts\n\n\n// src/zod-schema.ts\n\nfunction zodSchema(zodSchema2, options) {\n  var _a;\n  const useReferences = (_a = options == null ? void 0 : options.useReferences) != null ? _a : false;\n  return jsonSchema(\n    (0,zod_to_json_schema__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(zodSchema2, {\n      $refStrategy: useReferences ? \"root\" : \"none\",\n      target: \"jsonSchema7\"\n      // note: openai mode breaks various gemini conversions\n    }),\n    {\n      validate: (value) => {\n        const result = zodSchema2.safeParse(value);\n        return result.success ? { success: true, value: result.data } : { success: false, error: result.error };\n      }\n    }\n  );\n}\n\n// src/schema.ts\nvar schemaSymbol = Symbol.for(\"vercel.ai.schema\");\nfunction jsonSchema(jsonSchema2, {\n  validate\n} = {}) {\n  return {\n    [schemaSymbol]: true,\n    _type: void 0,\n    // should never be used directly\n    [_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.validatorSymbol]: true,\n    jsonSchema: jsonSchema2,\n    validate\n  };\n}\nfunction isSchema(value) {\n  return typeof value === \"object\" && value !== null && schemaSymbol in value && value[schemaSymbol] === true && \"jsonSchema\" in value && \"validate\" in value;\n}\nfunction asSchema(schema) {\n  return isSchema(schema) ? schema : zodSchema(schema);\n}\n\n// src/should-resubmit-messages.ts\nfunction shouldResubmitMessages({\n  originalMaxToolInvocationStep,\n  originalMessageCount,\n  maxSteps,\n  messages\n}) {\n  var _a;\n  const lastMessage = messages[messages.length - 1];\n  return (\n    // check if the feature is enabled:\n    maxSteps > 1 && // ensure there is a last message:\n    lastMessage != null && // ensure we actually have new steps (to prevent infinite loops in case of errors):\n    (messages.length > originalMessageCount || extractMaxToolInvocationStep(lastMessage.toolInvocations) !== originalMaxToolInvocationStep) && // check that next step is possible:\n    isAssistantMessageWithCompletedToolCalls(lastMessage) && // limit the number of automatic steps:\n    ((_a = extractMaxToolInvocationStep(lastMessage.toolInvocations)) != null ? _a : 0) < maxSteps\n  );\n}\nfunction isAssistantMessageWithCompletedToolCalls(message) {\n  if (message.role !== \"assistant\") {\n    return false;\n  }\n  const lastStepStartIndex = message.parts.reduce((lastIndex, part, index) => {\n    return part.type === \"step-start\" ? index : lastIndex;\n  }, -1);\n  const lastStepToolInvocations = message.parts.slice(lastStepStartIndex + 1).filter((part) => part.type === \"tool-invocation\");\n  return lastStepToolInvocations.length > 0 && lastStepToolInvocations.every((part) => \"result\" in part.toolInvocation);\n}\n\n// src/update-tool-call-result.ts\nfunction updateToolCallResult({\n  messages,\n  toolCallId,\n  toolResult: result\n}) {\n  var _a;\n  const lastMessage = messages[messages.length - 1];\n  const invocationPart = lastMessage.parts.find(\n    (part) => part.type === \"tool-invocation\" && part.toolInvocation.toolCallId === toolCallId\n  );\n  if (invocationPart == null) {\n    return;\n  }\n  const toolResult = {\n    ...invocationPart.toolInvocation,\n    state: \"result\",\n    result\n  };\n  invocationPart.toolInvocation = toolResult;\n  lastMessage.toolInvocations = (_a = lastMessage.toolInvocations) == null ? void 0 : _a.map(\n    (toolInvocation) => toolInvocation.toolCallId === toolCallId ? toolResult : toolInvocation\n  );\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ai-sdk/ui-utils/dist/index.mjs\n");

/***/ })

};
;