"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ai";
exports.ids = ["vendor-chunks/ai"];
exports.modules = {

/***/ "(ssr)/./node_modules/ai/react/dist/index.mjs":
/*!**********************************************!*\
  !*** ./node_modules/ai/react/dist/index.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   experimental_useObject: () => (/* binding */ experimental_useObject),\n/* harmony export */   useAssistant: () => (/* binding */ useAssistant),\n/* harmony export */   useChat: () => (/* binding */ useChat),\n/* harmony export */   useCompletion: () => (/* binding */ useCompletion)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ai-sdk/react */ \"(ssr)/./node_modules/@ai-sdk/react/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ experimental_useObject,useAssistant,useChat,useCompletion auto */ // react/index.ts\n\nvar useChat = _ai_sdk_react__WEBPACK_IMPORTED_MODULE_0__.useChat;\nvar useCompletion = _ai_sdk_react__WEBPACK_IMPORTED_MODULE_0__.useCompletion;\nvar useAssistant = _ai_sdk_react__WEBPACK_IMPORTED_MODULE_0__.useAssistant;\nvar experimental_useObject = _ai_sdk_react__WEBPACK_IMPORTED_MODULE_0__.experimental_useObject;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYWkvcmVhY3QvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBS087QUFLQSxJQUFNLFVBQVUsa0RBQVk7QUFLNUIsSUFBTSxnQkFBZ0Isd0RBQWtCO0FBS3hDLElBQU0sZUFBZSx1REFBaUI7QUFLdEMsSUFBTSx5QkFBeUIsaUVBQTJCIiwic291cmNlcyI6WyIvcm9vdC96ZW5nbGVpbGVpL3dvcmtzcGFjZS9pbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xuICB1c2VDaGF0IGFzIHVzZUNoYXRSZWFjdCxcbiAgdXNlQ29tcGxldGlvbiBhcyB1c2VDb21wbGV0aW9uUmVhY3QsXG4gIHVzZUFzc2lzdGFudCBhcyB1c2VBc3Npc3RhbnRSZWFjdCxcbiAgZXhwZXJpbWVudGFsX3VzZU9iamVjdCBhcyBleHBlcmltZW50YWxfdXNlT2JqZWN0UmVhY3QsXG59IGZyb20gJ0BhaS1zZGsvcmVhY3QnO1xuXG4vKipcbiAqIEBkZXByZWNhdGVkIFVzZSBgQGFpLXNkay9yZWFjdGAgaW5zdGVhZC5cbiAqL1xuZXhwb3J0IGNvbnN0IHVzZUNoYXQgPSB1c2VDaGF0UmVhY3Q7XG5cbi8qKlxuICogQGRlcHJlY2F0ZWQgVXNlIGBAYWktc2RrL3JlYWN0YCBpbnN0ZWFkLlxuICovXG5leHBvcnQgY29uc3QgdXNlQ29tcGxldGlvbiA9IHVzZUNvbXBsZXRpb25SZWFjdDtcblxuLyoqXG4gKiBAZGVwcmVjYXRlZCBVc2UgYEBhaS1zZGsvcmVhY3RgIGluc3RlYWQuXG4gKi9cbmV4cG9ydCBjb25zdCB1c2VBc3Npc3RhbnQgPSB1c2VBc3Npc3RhbnRSZWFjdDtcblxuLyoqXG4gKiBAZGVwcmVjYXRlZCBVc2UgYEBhaS1zZGsvcmVhY3RgIGluc3RlYWQuXG4gKi9cbmV4cG9ydCBjb25zdCBleHBlcmltZW50YWxfdXNlT2JqZWN0ID0gZXhwZXJpbWVudGFsX3VzZU9iamVjdFJlYWN0O1xuXG5leHBvcnQgdHlwZSB7XG4gIC8qKlxuICAgKiBAZGVwcmVjYXRlZCBVc2UgYEBhaS1zZGsvcmVhY3RgIGluc3RlYWQuXG4gICAqL1xuICBDcmVhdGVNZXNzYWdlLFxuXG4gIC8qKlxuICAgKiBAZGVwcmVjYXRlZCBVc2UgYEBhaS1zZGsvcmVhY3RgIGluc3RlYWQuXG4gICAqL1xuICBNZXNzYWdlLFxuXG4gIC8qKlxuICAgKiBAZGVwcmVjYXRlZCBVc2UgYEBhaS1zZGsvcmVhY3RgIGluc3RlYWQuXG4gICAqL1xuICBVc2VDaGF0T3B0aW9ucyxcblxuICAvKipcbiAgICogQGRlcHJlY2F0ZWQgVXNlIGBAYWktc2RrL3JlYWN0YCBpbnN0ZWFkLlxuICAgKi9cbiAgVXNlQ2hhdEhlbHBlcnMsXG59IGZyb20gJ0BhaS1zZGsvcmVhY3QnO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ai/react/dist/index.mjs\n");

/***/ })

};
;