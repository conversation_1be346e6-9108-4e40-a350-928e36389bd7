{"version": 3, "middleware": {}, "functions": {"/api/chat/route": {"files": ["server/server-reference-manifest.js", "server/app/api/chat/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/chat/route.js"], "name": "app/api/chat/route", "page": "/api/chat/route", "matchers": [{"regexp": "^/api/chat$", "originalSource": "/api/chat"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "tY1YBlEnE07LmckP8I5oMmtuHsrj9wbpzS8gYc4LZA8="}}}, "sortedMiddleware": []}