"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/report-chat.tsx":
/*!************************************!*\
  !*** ./components/report-chat.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReportChat: () => (/* binding */ ReportChat)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Send_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Send_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* __next_internal_client_entry_do_not_use__ ReportChat auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ReportChat(param) {\n    let { reportSummary } = param;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"welcome-message\",\n            role: \"assistant\",\n            content: \"您好，我是您的AI医疗助手。我已经了解了这份评估报告的内容。您可以就报告中的任何内容向我提问，比如手术风险、治疗建议、术后护理等。请问有什么我可以帮助您的吗？\",\n            timestamp: new Date()\n        }\n    ]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [apiConfig, setApiConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        baseUrl: \"https://api.openai.com/v1\",\n        apiKey: \"\",\n        model: \"gpt-3.5-turbo\"\n    });\n    const [isConfigOpen, setIsConfigOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tempConfig, setTempConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(apiConfig);\n    // 添加引用来跟踪消息容器和最后一条消息\n    const scrollAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 自动滚动到最新消息\n    const scrollToBottom = ()=>{\n        if (messagesEndRef.current) {\n            messagesEndRef.current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n    };\n    // 当消息列表更新时自动滚动\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReportChat.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ReportChat.useEffect\"], [\n        messages\n    ]);\n    const handleInputChange = (e)=>{\n        setInput(e.target.value);\n    };\n    const handleConfigSave = ()=>{\n        setApiConfig(tempConfig);\n        setIsConfigOpen(false);\n        setError(null);\n    };\n    const sendMessage = async (userMessage)=>{\n        if (!userMessage.trim()) return;\n        if (!apiConfig.apiKey.trim()) {\n            setError(\"请先在设置中配置 API Key\");\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        // 添加用户消息\n        const newUserMessage = {\n            id: \"user-\".concat(Date.now()),\n            role: \"user\",\n            content: userMessage,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newUserMessage\n            ]);\n        try {\n            var _response_body;\n            console.log(\"发送消息到 API:\", userMessage);\n            console.log(\"使用的 API 配置:\", {\n                baseUrl: apiConfig.baseUrl,\n                model: apiConfig.model,\n                hasApiKey: !!apiConfig.apiKey\n            });\n            // 准备发送给 OpenAI API 的消息\n            const apiMessages = [\n                {\n                    role: \"system\",\n                    content: '你是温州医科大学ETOCD项目的AI助手。以下是当前患者的术前评估报告摘要，请基于此摘要和你的医学知识库来回答用户的问题。\\n\\n报告摘要：\\n\"\"\"'.concat(reportSummary, '\"\"\"\\n\\n请注意：\\n1. 回答要专业、准确，但要用患者能理解的语言\\n2. 如果涉及诊断或治疗建议，请提醒患者咨询主治医生\\n3. 保持温和、耐心的语调\\n4. 如果问题超出报告范围，请说明并提供一般性建议')\n                },\n                ...messages.filter((msg)=>msg.role !== \"system\").map((msg)=>({\n                        role: msg.role,\n                        content: msg.content\n                    })),\n                {\n                    role: \"user\",\n                    content: userMessage\n                }\n            ];\n            console.log(\"发送到 OpenAI API 的消息数量:\", apiMessages.length);\n            // 构建 OpenAI API 请求\n            const openaiUrl = \"\".concat(apiConfig.baseUrl.replace(/\\/$/, \"\"), \"/chat/completions\");\n            const response = await fetch(openaiUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(apiConfig.apiKey)\n                },\n                body: JSON.stringify({\n                    model: apiConfig.model,\n                    messages: apiMessages,\n                    stream: true,\n                    temperature: 0.7,\n                    max_tokens: 2000\n                })\n            });\n            console.log(\"OpenAI API 响应状态:\", response.status, response.statusText);\n            if (!response.ok) {\n                let errorMessage = \"API请求失败 (\".concat(response.status, \"): \").concat(response.statusText);\n                try {\n                    var _errorData_error;\n                    const errorData = await response.json();\n                    console.log(\"错误响应数据:\", errorData);\n                    if ((_errorData_error = errorData.error) === null || _errorData_error === void 0 ? void 0 : _errorData_error.message) {\n                        errorMessage = errorData.error.message;\n                    }\n                } catch (e) {\n                    console.log(\"无法解析错误响应为 JSON\");\n                    const errorText = await response.text();\n                    console.log(\"错误响应文本:\", errorText);\n                    if (errorText) {\n                        errorMessage = errorText;\n                    }\n                }\n                throw new Error(errorMessage);\n            }\n            // 处理流式响应\n            const reader = (_response_body = response.body) === null || _response_body === void 0 ? void 0 : _response_body.getReader();\n            if (!reader) {\n                throw new Error(\"无法读取响应流\");\n            }\n            let assistantMessage = \"\";\n            const assistantMessageId = \"assistant-\".concat(Date.now());\n            // 添加空的助手消息\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        id: assistantMessageId,\n                        role: \"assistant\",\n                        content: \"\",\n                        timestamp: new Date()\n                    }\n                ]);\n            const decoder = new TextDecoder();\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) break;\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    console.log(\"收到数据块:\", chunk);\n                    // 解析 OpenAI 流式响应\n                    const lines = chunk.split(\"\\n\");\n                    for (const line of lines){\n                        const trimmedLine = line.trim();\n                        if (trimmedLine === \"\") continue;\n                        if (trimmedLine === \"data: [DONE]\") {\n                            console.log(\"流式响应完成\");\n                            break;\n                        }\n                        if (trimmedLine.startsWith(\"data: \")) {\n                            const jsonStr = trimmedLine.slice(6);\n                            try {\n                                const data = JSON.parse(jsonStr);\n                                if (data.choices && data.choices[0] && data.choices[0].delta) {\n                                    const delta = data.choices[0].delta;\n                                    if (delta.content) {\n                                        assistantMessage += delta.content;\n                                        // 更新消息\n                                        setMessages((prev)=>prev.map((msg)=>msg.id === assistantMessageId ? {\n                                                    ...msg,\n                                                    content: assistantMessage\n                                                } : msg));\n                                    }\n                                }\n                            } catch (parseError) {\n                                console.log(\"解析流式数据失败:\", parseError, \"数据:\", jsonStr);\n                            }\n                        }\n                    }\n                }\n                // 如果没有收到任何内容，显示错误\n                if (!assistantMessage.trim()) {\n                    throw new Error(\"AI 服务没有返回有效响应\");\n                }\n                console.log(\"完整的助手回复:\", assistantMessage);\n            } finally{\n                reader.releaseLock();\n            }\n        } catch (err) {\n            console.error(\"发送消息时出错:\", err);\n            let errorMessage = err.message || \"发送消息时出现未知错误\";\n            // 处理常见错误\n            if (errorMessage.includes(\"401\")) {\n                errorMessage = \"API Key 无效，请检查配置\";\n            } else if (errorMessage.includes(\"429\")) {\n                errorMessage = \"请求过于频繁，请稍后再试\";\n            } else if (errorMessage.includes(\"404\")) {\n                errorMessage = \"API 端点不存在，请检查 Base URL 配置\";\n            } else if (errorMessage.includes(\"NetworkError\") || errorMessage.includes(\"Failed to fetch\")) {\n                errorMessage = \"网络连接失败，请检查网络或 Base URL 是否正确\";\n            }\n            setError(errorMessage);\n            // 移除用户消息（如果发送失败）\n            setMessages((prev)=>prev.filter((msg)=>msg.id !== newUserMessage.id));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSubmit = async (userMessage)=>{\n        if (!userMessage.trim() || isLoading) return;\n        await sendMessage(userMessage);\n    };\n    const testApiConnection = async ()=>{\n        if (!apiConfig.apiKey.trim()) {\n            setError(\"请先配置 API Key\");\n            return;\n        }\n        setError(null);\n        try {\n            console.log(\"测试 OpenAI API 连接...\");\n            const openaiUrl = \"\".concat(apiConfig.baseUrl.replace(/\\/$/, \"\"), \"/chat/completions\");\n            const response = await fetch(openaiUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(apiConfig.apiKey)\n                },\n                body: JSON.stringify({\n                    model: apiConfig.model,\n                    messages: [\n                        {\n                            role: \"user\",\n                            content: \"你好，这是一个连接测试，请简单回复确认\"\n                        }\n                    ],\n                    max_tokens: 50\n                })\n            });\n            console.log(\"测试响应状态:\", response.status);\n            if (!response.ok) {\n                var _errorData_error;\n                const errorData = await response.json();\n                console.error(\"测试响应错误:\", errorData);\n                throw new Error(((_errorData_error = errorData.error) === null || _errorData_error === void 0 ? void 0 : _errorData_error.message) || \"HTTP \".concat(response.status));\n            } else {\n                const data = await response.json();\n                console.log(\"API 连接测试成功:\", data);\n                setError(null);\n                // 显示成功消息\n                setMessages((prev)=>[\n                        ...prev,\n                        {\n                            id: \"test-\".concat(Date.now()),\n                            role: \"assistant\",\n                            content: \"✅ API 连接测试成功！配置正确。\",\n                            timestamp: new Date()\n                        }\n                    ]);\n            }\n        } catch (err) {\n            console.error(\"API 连接测试失败:\", err);\n            setError(\"API 连接测试失败: \".concat(err.message));\n        }\n    };\n    const clearMessages = ()=>{\n        setMessages([\n            {\n                id: \"welcome-message\",\n                role: \"assistant\",\n                content: \"您好，我是您的AI医疗助手。我已经了解了这份评估报告的内容。您可以就报告中的任何内容向我提问，比如手术风险、治疗建议、术后护理等。请问有什么我可以帮助您的吗？\",\n                timestamp: new Date()\n            }\n        ]);\n        setError(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"w-full h-[70vh] flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            \"AI 智能问答\",\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Send_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-red-600 bg-red-50 p-2 rounded\",\n                        children: [\n                            \"错误: \",\n                            error.message\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 19\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                lineNumber: 355,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"flex-1 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                    className: \"h-full pr-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            messages.filter((m)=>m.role !== \"system\").map((m)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start gap-4 \".concat(m.role === \"user\" ? \"justify-end\" : \"\"),\n                                    children: [\n                                        m.role === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                                            className: \"h-8 w-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AvatarImage, {\n                                                    src: \"/placeholder.svg?width=32&height=32\"\n                                                }, void 0, false, {\n                                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                                                    children: \"AI\"\n                                                }, void 0, false, {\n                                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"rounded-lg p-3 text-sm max-w-[75%] \".concat(m.role === \"user\" ? \"bg-primary text-primary-foreground\" : \"bg-muted\"),\n                                            children: m.content\n                                        }, void 0, false, {\n                                            fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 19\n                                        }, this),\n                                        m.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                                            className: \"h-8 w-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AvatarImage, {\n                                                    src: \"/placeholder.svg?width=32&height=32\"\n                                                }, void 0, false, {\n                                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                                                    children: \"ME\"\n                                                }, void 0, false, {\n                                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, m.id, true, {\n                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 17\n                                }, this)),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                                        className: \"h-8 w-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AvatarImage, {\n                                                src: \"/placeholder.svg?width=32&height=32\"\n                                            }, void 0, false, {\n                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                                                children: \"AI\"\n                                            }, void 0, false, {\n                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-lg p-3 text-sm bg-muted\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Send_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"正在思考中...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                lineNumber: 362,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardFooter, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: onSubmit,\n                    className: \"flex w-full items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                            value: input,\n                            onChange: handleInputChange,\n                            placeholder: \"请就报告内容提问...\",\n                            disabled: isLoading\n                        }, void 0, false, {\n                            fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            type: \"submit\",\n                            disabled: isLoading || !input.trim(),\n                            children: [\n                                isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Send_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 26\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Send_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 73\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"发送\"\n                                }, void 0, false, {\n                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                            lineNumber: 408,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                    lineNumber: 406,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                lineNumber: 405,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n        lineNumber: 354,\n        columnNumber: 5\n    }, this);\n}\n_s(ReportChat, \"gk7Crv0HWH1S3OjxYadIieqG/gU=\");\n_c = ReportChat;\nvar _c;\n$RefreshReg$(_c, \"ReportChat\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/report-chat.tsx\n"));

/***/ })

});