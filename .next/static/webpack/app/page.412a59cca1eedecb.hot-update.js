"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/report-chat.tsx":
/*!************************************!*\
  !*** ./components/report-chat.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReportChat: () => (/* binding */ ReportChat)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_RefreshCw_Send_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,RefreshCw,Send,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_RefreshCw_Send_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,RefreshCw,Send,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_RefreshCw_Send_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,RefreshCw,Send,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_RefreshCw_Send_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,RefreshCw,Send,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_RefreshCw_Send_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,RefreshCw,Send,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* __next_internal_client_entry_do_not_use__ ReportChat auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction ReportChat(param) {\n    let { reportSummary } = param;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"welcome-message\",\n            role: \"assistant\",\n            content: \"您好，我是您的AI医疗助手。我已经了解了这份评估报告的内容。您可以就报告中的任何内容向我提问，比如手术风险、治疗建议、术后护理等。请问有什么我可以帮助您的吗？\",\n            timestamp: new Date()\n        }\n    ]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [apiConfig, setApiConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        baseUrl: \"https://api.openai.com/v1\",\n        apiKey: \"\",\n        model: \"gpt-3.5-turbo\"\n    });\n    const [isConfigOpen, setIsConfigOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tempConfig, setTempConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(apiConfig);\n    // 添加引用来跟踪消息容器和最后一条消息\n    const scrollAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 自动滚动到最新消息\n    const scrollToBottom = ()=>{\n        if (messagesEndRef.current) {\n            messagesEndRef.current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n    };\n    // 当消息列表更新时自动滚动\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReportChat.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ReportChat.useEffect\"], [\n        messages\n    ]);\n    const handleInputChange = (e)=>{\n        setInput(e.target.value);\n    };\n    const handleConfigSave = ()=>{\n        setApiConfig(tempConfig);\n        setIsConfigOpen(false);\n        setError(null);\n    };\n    const sendMessage = async (userMessage)=>{\n        if (!userMessage.trim()) return;\n        if (!apiConfig.apiKey.trim()) {\n            setError(\"请先在设置中配置 API Key\");\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        // 添加用户消息\n        const newUserMessage = {\n            id: \"user-\".concat(Date.now()),\n            role: \"user\",\n            content: userMessage,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newUserMessage\n            ]);\n        try {\n            var _response_body;\n            console.log(\"发送消息到 API:\", userMessage);\n            console.log(\"使用的 API 配置:\", {\n                baseUrl: apiConfig.baseUrl,\n                model: apiConfig.model,\n                hasApiKey: !!apiConfig.apiKey\n            });\n            // 准备发送给 OpenAI API 的消息\n            const apiMessages = [\n                {\n                    role: \"system\",\n                    content: '你是温州医科大学ETOCD项目的AI助手。以下是当前患者的术前评估报告摘要，请基于此摘要和你的医学知识库来回答用户的问题。\\n\\n报告摘要：\\n\"\"\"'.concat(reportSummary, '\"\"\"\\n\\n请注意：\\n1. 回答要专业、准确，但要用患者能理解的语言\\n2. 如果涉及诊断或治疗建议，请提醒患者咨询主治医生\\n3. 保持温和、耐心的语调\\n4. 如果问题超出报告范围，请说明并提供一般性建议')\n                },\n                ...messages.filter((msg)=>msg.role !== \"system\").map((msg)=>({\n                        role: msg.role,\n                        content: msg.content\n                    })),\n                {\n                    role: \"user\",\n                    content: userMessage\n                }\n            ];\n            console.log(\"发送到 OpenAI API 的消息数量:\", apiMessages.length);\n            // 构建 OpenAI API 请求\n            const openaiUrl = \"\".concat(apiConfig.baseUrl.replace(/\\/$/, \"\"), \"/chat/completions\");\n            const response = await fetch(openaiUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(apiConfig.apiKey)\n                },\n                body: JSON.stringify({\n                    model: apiConfig.model,\n                    messages: apiMessages,\n                    stream: true,\n                    temperature: 0.7,\n                    max_tokens: 2000\n                })\n            });\n            console.log(\"OpenAI API 响应状态:\", response.status, response.statusText);\n            if (!response.ok) {\n                let errorMessage = \"API请求失败 (\".concat(response.status, \"): \").concat(response.statusText);\n                try {\n                    var _errorData_error;\n                    const errorData = await response.json();\n                    console.log(\"错误响应数据:\", errorData);\n                    if ((_errorData_error = errorData.error) === null || _errorData_error === void 0 ? void 0 : _errorData_error.message) {\n                        errorMessage = errorData.error.message;\n                    }\n                } catch (e) {\n                    console.log(\"无法解析错误响应为 JSON\");\n                    const errorText = await response.text();\n                    console.log(\"错误响应文本:\", errorText);\n                    if (errorText) {\n                        errorMessage = errorText;\n                    }\n                }\n                throw new Error(errorMessage);\n            }\n            // 处理流式响应\n            const reader = (_response_body = response.body) === null || _response_body === void 0 ? void 0 : _response_body.getReader();\n            if (!reader) {\n                throw new Error(\"无法读取响应流\");\n            }\n            let assistantMessage = \"\";\n            const assistantMessageId = \"assistant-\".concat(Date.now());\n            // 添加空的助手消息\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        id: assistantMessageId,\n                        role: \"assistant\",\n                        content: \"\",\n                        timestamp: new Date()\n                    }\n                ]);\n            const decoder = new TextDecoder();\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) break;\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    console.log(\"收到数据块:\", chunk);\n                    // 解析 OpenAI 流式响应\n                    const lines = chunk.split(\"\\n\");\n                    for (const line of lines){\n                        const trimmedLine = line.trim();\n                        if (trimmedLine === \"\") continue;\n                        if (trimmedLine === \"data: [DONE]\") {\n                            console.log(\"流式响应完成\");\n                            break;\n                        }\n                        if (trimmedLine.startsWith(\"data: \")) {\n                            const jsonStr = trimmedLine.slice(6);\n                            try {\n                                const data = JSON.parse(jsonStr);\n                                if (data.choices && data.choices[0] && data.choices[0].delta) {\n                                    const delta = data.choices[0].delta;\n                                    if (delta.content) {\n                                        assistantMessage += delta.content;\n                                        // 更新消息\n                                        setMessages((prev)=>prev.map((msg)=>msg.id === assistantMessageId ? {\n                                                    ...msg,\n                                                    content: assistantMessage\n                                                } : msg));\n                                    }\n                                }\n                            } catch (parseError) {\n                                console.log(\"解析流式数据失败:\", parseError, \"数据:\", jsonStr);\n                            }\n                        }\n                    }\n                }\n                // 如果没有收到任何内容，显示错误\n                if (!assistantMessage.trim()) {\n                    throw new Error(\"AI 服务没有返回有效响应\");\n                }\n                console.log(\"完整的助手回复:\", assistantMessage);\n            } finally{\n                reader.releaseLock();\n            }\n        } catch (err) {\n            console.error(\"发送消息时出错:\", err);\n            let errorMessage = err.message || \"发送消息时出现未知错误\";\n            // 处理常见错误\n            if (errorMessage.includes(\"401\")) {\n                errorMessage = \"API Key 无效，请检查配置\";\n            } else if (errorMessage.includes(\"429\")) {\n                errorMessage = \"请求过于频繁，请稍后再试\";\n            } else if (errorMessage.includes(\"404\")) {\n                errorMessage = \"API 端点不存在，请检查 Base URL 配置\";\n            } else if (errorMessage.includes(\"NetworkError\") || errorMessage.includes(\"Failed to fetch\")) {\n                errorMessage = \"网络连接失败，请检查网络或 Base URL 是否正确\";\n            }\n            setError(errorMessage);\n            // 移除用户消息（如果发送失败）\n            setMessages((prev)=>prev.filter((msg)=>msg.id !== newUserMessage.id));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const testApiConnection = async ()=>{\n        if (!apiConfig.apiKey.trim()) {\n            setError(\"请先配置 API Key\");\n            return;\n        }\n        setError(null);\n        try {\n            console.log(\"测试 OpenAI API 连接...\");\n            const openaiUrl = \"\".concat(apiConfig.baseUrl.replace(/\\/$/, \"\"), \"/chat/completions\");\n            const response = await fetch(openaiUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(apiConfig.apiKey)\n                },\n                body: JSON.stringify({\n                    model: apiConfig.model,\n                    messages: [\n                        {\n                            role: \"user\",\n                            content: \"你好，这是一个连接测试，请简单回复确认\"\n                        }\n                    ],\n                    max_tokens: 50\n                })\n            });\n            console.log(\"测试响应状态:\", response.status);\n            if (!response.ok) {\n                var _errorData_error;\n                const errorData = await response.json();\n                console.error(\"测试响应错误:\", errorData);\n                throw new Error(((_errorData_error = errorData.error) === null || _errorData_error === void 0 ? void 0 : _errorData_error.message) || \"HTTP \".concat(response.status));\n            } else {\n                const data = await response.json();\n                console.log(\"API 连接测试成功:\", data);\n                setError(null);\n                // 显示成功消息\n                setMessages((prev)=>[\n                        ...prev,\n                        {\n                            id: \"test-\".concat(Date.now()),\n                            role: \"assistant\",\n                            content: \"✅ API 连接测试成功！配置正确。\",\n                            timestamp: new Date()\n                        }\n                    ]);\n            }\n        } catch (err) {\n            console.error(\"API 连接测试失败:\", err);\n            setError(\"API 连接测试失败: \".concat(err.message));\n        }\n    };\n    const clearMessages = ()=>{\n        setMessages([\n            {\n                id: \"welcome-message\",\n                role: \"assistant\",\n                content: \"您好，我是您的AI医疗助手。我已经了解了这份评估报告的内容。您可以就报告中的任何内容向我提问，比如手术风险、治疗建议、术后护理等。请问有什么我可以帮助您的吗？\",\n                timestamp: new Date()\n            }\n        ]);\n        setError(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"w-full h-[70vh] flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            \"AI 智能问答\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_RefreshCw_Send_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 27\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: testApiConnection,\n                                        title: \"测试API连接\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_RefreshCw_Send_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: clearMessages,\n                                        title: \"清空对话\",\n                                        children: \"清空\"\n                                    }, void 0, false, {\n                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.Dialog, {\n                                        open: isConfigOpen,\n                                        onOpenChange: setIsConfigOpen,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    title: \"API设置\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_RefreshCw_Send_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogContent, {\n                                                className: \"sm:max-w-[600px]\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogHeader, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogTitle, {\n                                                            children: \"API 配置设置\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid gap-4 py-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                        htmlFor: \"baseUrl\",\n                                                                        children: \"Base URL\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                        lineNumber: 376,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        id: \"baseUrl\",\n                                                                        value: tempConfig.baseUrl,\n                                                                        onChange: (e)=>setTempConfig((prev)=>({\n                                                                                    ...prev,\n                                                                                    baseUrl: e.target.value\n                                                                                })),\n                                                                        placeholder: \"https://api.openai.com/v1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                        lineNumber: 377,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: \"OpenAI API 的基础URL，如使用第三方代理请修改此地址\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                        htmlFor: \"apiKey\",\n                                                                        children: \"API Key\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                        lineNumber: 387,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        id: \"apiKey\",\n                                                                        type: \"password\",\n                                                                        value: tempConfig.apiKey,\n                                                                        onChange: (e)=>setTempConfig((prev)=>({\n                                                                                    ...prev,\n                                                                                    apiKey: e.target.value\n                                                                                })),\n                                                                        placeholder: \"sk-...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                        lineNumber: 388,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: \"您的 OpenAI API Key，将安全存储在本地\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                        lineNumber: 395,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                        htmlFor: \"model\",\n                                                                        children: \"模型\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                        lineNumber: 399,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        id: \"model\",\n                                                                        value: tempConfig.model,\n                                                                        onChange: (e)=>setTempConfig((prev)=>({\n                                                                                    ...prev,\n                                                                                    model: e.target.value\n                                                                                })),\n                                                                        placeholder: \"gpt-3.5-turbo\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                        lineNumber: 400,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: \"使用的AI模型，如 gpt-3.5-turbo, gpt-4 等\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                        lineNumber: 406,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                lineNumber: 398,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-end gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>setIsConfigOpen(false),\n                                                                children: \"取消\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                onClick: handleConfigSave,\n                                                                children: \"保存配置\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                        variant: \"destructive\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_RefreshCw_Send_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.AlertDescription, {\n                                className: \"flex flex-col gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"错误:\"\n                                            }, void 0, false, {\n                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" \",\n                                            error\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs opacity-75\",\n                                        children: \"提示: 请检查 API 配置和网络连接。点击设置按钮配置 API Key 和 Base URL。\"\n                                    }, void 0, false, {\n                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setError(null),\n                                        className: \"w-fit\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_RefreshCw_Send_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-3 w-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"清除错误\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-muted-foreground\",\n                        children: [\n                            \"状态: \",\n                            apiConfig.apiKey ? \"已配置\" : \"未配置\",\n                            \" API Key | 模型: \",\n                            apiConfig.model,\n                            \" | 端点: \",\n                            apiConfig.baseUrl\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                        lineNumber: 439,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                lineNumber: 350,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"flex-1 overflow-hidden\",\n                ref: scrollAreaRef,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                    className: \"h-full pr-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            messages.map((m)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start gap-4 \".concat(m.role === \"user\" ? \"justify-end\" : \"\"),\n                                    children: [\n                                        m.role === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                                            className: \"h-8 w-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                                                children: \"AI\"\n                                            }, void 0, false, {\n                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"rounded-lg p-3 text-sm max-w-[75%] \".concat(m.role === \"user\" ? \"bg-primary text-primary-foreground\" : \"bg-muted\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"whitespace-pre-wrap\",\n                                                    children: m.content\n                                                }, void 0, false, {\n                                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 19\n                                                }, this),\n                                                m.timestamp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs opacity-60 mt-1\",\n                                                    children: m.timestamp.toLocaleTimeString()\n                                                }, void 0, false, {\n                                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 35\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 17\n                                        }, this),\n                                        m.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                                            className: \"h-8 w-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                                                children: \"我\"\n                                            }, void 0, false, {\n                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, m.id, true, {\n                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 15\n                                }, this)),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                                        className: \"h-8 w-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                                            children: \"AI\"\n                                        }, void 0, false, {\n                                            fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-lg p-3 text-sm bg-muted\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_RefreshCw_Send_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin inline mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"正在思考中...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                lineNumber: 470,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: messagesEndRef\n                            }, void 0, false, {\n                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                lineNumber: 481,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                        lineNumber: 446,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                    lineNumber: 445,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                lineNumber: 444,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardFooter, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex w-full items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                            value: input,\n                            onChange: handleInputChange,\n                            placeholder: apiConfig.apiKey ? \"请就报告内容提问...\" : \"请先配置 API Key\",\n                            disabled: isLoading || !apiConfig.apiKey,\n                            onKeyDown: (e)=>{\n                                if (e.key === \"Enter\" && !e.shiftKey) {\n                                    e.preventDefault();\n                                    if (!isLoading && input.trim() && apiConfig.apiKey) {\n                                        const userMessage = input.trim();\n                                        setInput(\"\");\n                                        sendMessage(userMessage);\n                                    }\n                                }\n                            }\n                        }, void 0, false, {\n                            fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                            lineNumber: 488,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>{\n                                if (!isLoading && input.trim() && apiConfig.apiKey) {\n                                    const userMessage = input.trim();\n                                    setInput(\"\");\n                                    sendMessage(userMessage);\n                                }\n                            },\n                            disabled: isLoading || !input.trim() || !apiConfig.apiKey,\n                            children: [\n                                isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_RefreshCw_Send_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 26\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_RefreshCw_Send_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 73\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"发送\"\n                                }, void 0, false, {\n                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                    lineNumber: 515,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                            lineNumber: 504,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                    lineNumber: 487,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                lineNumber: 486,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n        lineNumber: 349,\n        columnNumber: 5\n    }, this);\n}\n_s(ReportChat, \"gk7Crv0HWH1S3OjxYadIieqG/gU=\");\n_c = ReportChat;\nvar _c;\n$RefreshReg$(_c, \"ReportChat\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/report-chat.tsx\n"));

/***/ })

});