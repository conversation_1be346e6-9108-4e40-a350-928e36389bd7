"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/report-chat.tsx":
/*!************************************!*\
  !*** ./components/report-chat.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReportChat: () => (/* binding */ ReportChat)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Send_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Send_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* __next_internal_client_entry_do_not_use__ ReportChat auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ReportChat(param) {\n    let { reportSummary } = param;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"welcome-message\",\n            role: \"assistant\",\n            content: \"您好，我是您的AI医疗助手。我已经了解了这份评估报告的内容。您可以就报告中的任何内容向我提问，比如手术风险、治疗建议、术后护理等。请问有什么我可以帮助您的吗？\",\n            timestamp: new Date()\n        }\n    ]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [apiConfig, setApiConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        baseUrl: \"https://api.openai.com/v1\",\n        apiKey: \"\",\n        model: \"gpt-3.5-turbo\"\n    });\n    const [isConfigOpen, setIsConfigOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tempConfig, setTempConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(apiConfig);\n    // 添加引用来跟踪消息容器和最后一条消息\n    const scrollAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 自动滚动到最新消息\n    const scrollToBottom = ()=>{\n        if (messagesEndRef.current) {\n            messagesEndRef.current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n    };\n    // 当消息列表更新时自动滚动\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReportChat.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ReportChat.useEffect\"], [\n        messages\n    ]);\n    const handleInputChange = (e)=>{\n        setInput(e.target.value);\n    };\n    const handleConfigSave = ()=>{\n        setApiConfig(tempConfig);\n        setIsConfigOpen(false);\n        setError(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"w-full h-[70vh] flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            \"AI 智能问答\",\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Send_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-red-600 bg-red-50 p-2 rounded\",\n                        children: [\n                            \"错误: \",\n                            error.message\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 19\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"flex-1 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                    className: \"h-full pr-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            messages.filter((m)=>m.role !== \"system\").map((m)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start gap-4 \".concat(m.role === \"user\" ? \"justify-end\" : \"\"),\n                                    children: [\n                                        m.role === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                                            className: \"h-8 w-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AvatarImage, {\n                                                    src: \"/placeholder.svg?width=32&height=32\"\n                                                }, void 0, false, {\n                                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                                                    children: \"AI\"\n                                                }, void 0, false, {\n                                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"rounded-lg p-3 text-sm max-w-[75%] \".concat(m.role === \"user\" ? \"bg-primary text-primary-foreground\" : \"bg-muted\"),\n                                            children: m.content\n                                        }, void 0, false, {\n                                            fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 19\n                                        }, this),\n                                        m.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                                            className: \"h-8 w-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AvatarImage, {\n                                                    src: \"/placeholder.svg?width=32&height=32\"\n                                                }, void 0, false, {\n                                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                                                    children: \"ME\"\n                                                }, void 0, false, {\n                                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, m.id, true, {\n                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 17\n                                }, this)),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                                        className: \"h-8 w-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AvatarImage, {\n                                                src: \"/placeholder.svg?width=32&height=32\"\n                                            }, void 0, false, {\n                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                                                children: \"AI\"\n                                            }, void 0, false, {\n                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-lg p-3 text-sm bg-muted\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Send_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"正在思考中...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardFooter, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: onSubmit,\n                    className: \"flex w-full items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                            value: input,\n                            onChange: handleInputChange,\n                            placeholder: \"请就报告内容提问...\",\n                            disabled: isLoading\n                        }, void 0, false, {\n                            fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            type: \"submit\",\n                            disabled: isLoading || !input.trim(),\n                            children: [\n                                isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Send_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 26\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Send_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 73\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"发送\"\n                                }, void 0, false, {\n                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n_s(ReportChat, \"gk7Crv0HWH1S3OjxYadIieqG/gU=\");\n_c = ReportChat;\nvar _c;\n$RefreshReg$(_c, \"ReportChat\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/report-chat.tsx\n"));

/***/ })

});