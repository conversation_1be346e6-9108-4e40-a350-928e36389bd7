"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/report-chat.tsx":
/*!************************************!*\
  !*** ./components/report-chat.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReportChat: () => (/* binding */ ReportChat)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_RefreshCw_Send_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,RefreshCw,Send,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_RefreshCw_Send_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,RefreshCw,Send,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_RefreshCw_Send_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,RefreshCw,Send,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_RefreshCw_Send_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,RefreshCw,Send,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_RefreshCw_Send_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,RefreshCw,Send,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* __next_internal_client_entry_do_not_use__ ReportChat auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction ReportChat(param) {\n    let { reportSummary } = param;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"welcome-message\",\n            role: \"assistant\",\n            content: \"您好，我是您的AI医疗助手。我已经了解了这份评估报告的内容。您可以就报告中的任何内容向我提问，比如手术风险、治疗建议、术后护理等。请问有什么我可以帮助您的吗？\",\n            timestamp: new Date()\n        }\n    ]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [apiConfig, setApiConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        baseUrl: \"https://api.openai.com/v1\",\n        apiKey: \"\",\n        model: \"gpt-3.5-turbo\"\n    });\n    const [isConfigOpen, setIsConfigOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tempConfig, setTempConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(apiConfig);\n    // 添加引用来跟踪消息容器和最后一条消息\n    const scrollAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 自动滚动到最新消息\n    const scrollToBottom = ()=>{\n        if (messagesEndRef.current) {\n            messagesEndRef.current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n    };\n    // 当消息列表更新时自动滚动\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReportChat.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ReportChat.useEffect\"], [\n        messages\n    ]);\n    const handleInputChange = (e)=>{\n        setInput(e.target.value);\n    };\n    const handleConfigSave = ()=>{\n        setApiConfig(tempConfig);\n        setIsConfigOpen(false);\n        setError(null);\n    };\n    const sendMessage = async (userMessage)=>{\n        if (!userMessage.trim()) return;\n        if (!apiConfig.apiKey.trim()) {\n            setError(\"请先在设置中配置 API Key\");\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        // 添加用户消息\n        const newUserMessage = {\n            id: \"user-\".concat(Date.now()),\n            role: \"user\",\n            content: userMessage,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newUserMessage\n            ]);\n        try {\n            var _response_body;\n            console.log(\"发送消息到 API:\", userMessage);\n            console.log(\"使用的 API 配置:\", {\n                baseUrl: apiConfig.baseUrl,\n                model: apiConfig.model,\n                hasApiKey: !!apiConfig.apiKey\n            });\n            // 准备发送给 OpenAI API 的消息\n            const apiMessages = [\n                {\n                    role: \"system\",\n                    content: '你是温州医科大学ETOCD项目的AI助手。以下是当前患者的术前评估报告摘要，请基于此摘要和你的医学知识库来回答用户的问题。\\n\\n报告摘要：\\n\"\"\"'.concat(reportSummary, '\"\"\"\\n\\n请注意：\\n1. 回答要专业、准确，但要用患者能理解的语言\\n2. 如果涉及诊断或治疗建议，请提醒患者咨询主治医生\\n3. 保持温和、耐心的语调\\n4. 如果问题超出报告范围，请说明并提供一般性建议')\n                },\n                ...messages.filter((msg)=>msg.role !== \"system\").map((msg)=>({\n                        role: msg.role,\n                        content: msg.content\n                    })),\n                {\n                    role: \"user\",\n                    content: userMessage\n                }\n            ];\n            console.log(\"发送到 OpenAI API 的消息数量:\", apiMessages.length);\n            // 构建 OpenAI API 请求\n            const openaiUrl = \"\".concat(apiConfig.baseUrl.replace(/\\/$/, \"\"), \"/chat/completions\");\n            const response = await fetch(openaiUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(apiConfig.apiKey)\n                },\n                body: JSON.stringify({\n                    model: apiConfig.model,\n                    messages: apiMessages,\n                    stream: true,\n                    temperature: 0.7,\n                    max_tokens: 2000\n                })\n            });\n            console.log(\"OpenAI API 响应状态:\", response.status, response.statusText);\n            if (!response.ok) {\n                let errorMessage = \"API请求失败 (\".concat(response.status, \"): \").concat(response.statusText);\n                try {\n                    var _errorData_error;\n                    const errorData = await response.json();\n                    console.log(\"错误响应数据:\", errorData);\n                    if ((_errorData_error = errorData.error) === null || _errorData_error === void 0 ? void 0 : _errorData_error.message) {\n                        errorMessage = errorData.error.message;\n                    }\n                } catch (e) {\n                    console.log(\"无法解析错误响应为 JSON\");\n                    const errorText = await response.text();\n                    console.log(\"错误响应文本:\", errorText);\n                    if (errorText) {\n                        errorMessage = errorText;\n                    }\n                }\n                throw new Error(errorMessage);\n            }\n            // 处理流式响应\n            const reader = (_response_body = response.body) === null || _response_body === void 0 ? void 0 : _response_body.getReader();\n            if (!reader) {\n                throw new Error(\"无法读取响应流\");\n            }\n            let assistantMessage = \"\";\n            const assistantMessageId = \"assistant-\".concat(Date.now());\n            // 添加空的助手消息\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        id: assistantMessageId,\n                        role: \"assistant\",\n                        content: \"\",\n                        timestamp: new Date()\n                    }\n                ]);\n            const decoder = new TextDecoder();\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) break;\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    console.log(\"收到数据块:\", chunk);\n                    // 解析 OpenAI 流式响应\n                    const lines = chunk.split(\"\\n\");\n                    for (const line of lines){\n                        const trimmedLine = line.trim();\n                        if (trimmedLine === \"\") continue;\n                        if (trimmedLine === \"data: [DONE]\") {\n                            console.log(\"流式响应完成\");\n                            break;\n                        }\n                        if (trimmedLine.startsWith(\"data: \")) {\n                            const jsonStr = trimmedLine.slice(6);\n                            try {\n                                const data = JSON.parse(jsonStr);\n                                if (data.choices && data.choices[0] && data.choices[0].delta) {\n                                    const delta = data.choices[0].delta;\n                                    if (delta.content) {\n                                        assistantMessage += delta.content;\n                                        // 更新消息\n                                        setMessages((prev)=>prev.map((msg)=>msg.id === assistantMessageId ? {\n                                                    ...msg,\n                                                    content: assistantMessage\n                                                } : msg));\n                                    }\n                                }\n                            } catch (parseError) {\n                                console.log(\"解析流式数据失败:\", parseError, \"数据:\", jsonStr);\n                            }\n                        }\n                    }\n                }\n                // 如果没有收到任何内容，显示错误\n                if (!assistantMessage.trim()) {\n                    throw new Error(\"AI 服务没有返回有效响应\");\n                }\n                console.log(\"完整的助手回复:\", assistantMessage);\n            } finally{\n                reader.releaseLock();\n            }\n        } catch (err) {\n            console.error(\"发送消息时出错:\", err);\n            let errorMessage = err.message || \"发送消息时出现未知错误\";\n            // 处理常见错误\n            if (errorMessage.includes(\"401\")) {\n                errorMessage = \"API Key 无效，请检查配置\";\n            } else if (errorMessage.includes(\"429\")) {\n                errorMessage = \"请求过于频繁，请稍后再试\";\n            } else if (errorMessage.includes(\"404\")) {\n                errorMessage = \"API 端点不存在，请检查 Base URL 配置\";\n            } else if (errorMessage.includes(\"NetworkError\") || errorMessage.includes(\"Failed to fetch\")) {\n                errorMessage = \"网络连接失败，请检查网络或 Base URL 是否正确\";\n            }\n            setError(errorMessage);\n            // 移除用户消息（如果发送失败）\n            setMessages((prev)=>prev.filter((msg)=>msg.id !== newUserMessage.id));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSubmit = async (userMessage)=>{\n        if (!userMessage.trim() || isLoading) return;\n        await sendMessage(userMessage);\n    };\n    const testApiConnection = async ()=>{\n        if (!apiConfig.apiKey.trim()) {\n            setError(\"请先配置 API Key\");\n            return;\n        }\n        setError(null);\n        try {\n            console.log(\"测试 OpenAI API 连接...\");\n            const openaiUrl = \"\".concat(apiConfig.baseUrl.replace(/\\/$/, \"\"), \"/chat/completions\");\n            const response = await fetch(openaiUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(apiConfig.apiKey)\n                },\n                body: JSON.stringify({\n                    model: apiConfig.model,\n                    messages: [\n                        {\n                            role: \"user\",\n                            content: \"你好，这是一个连接测试，请简单回复确认\"\n                        }\n                    ],\n                    max_tokens: 50\n                })\n            });\n            console.log(\"测试响应状态:\", response.status);\n            if (!response.ok) {\n                var _errorData_error;\n                const errorData = await response.json();\n                console.error(\"测试响应错误:\", errorData);\n                throw new Error(((_errorData_error = errorData.error) === null || _errorData_error === void 0 ? void 0 : _errorData_error.message) || \"HTTP \".concat(response.status));\n            } else {\n                const data = await response.json();\n                console.log(\"API 连接测试成功:\", data);\n                setError(null);\n                // 显示成功消息\n                setMessages((prev)=>[\n                        ...prev,\n                        {\n                            id: \"test-\".concat(Date.now()),\n                            role: \"assistant\",\n                            content: \"✅ API 连接测试成功！配置正确。\",\n                            timestamp: new Date()\n                        }\n                    ]);\n            }\n        } catch (err) {\n            console.error(\"API 连接测试失败:\", err);\n            setError(\"API 连接测试失败: \".concat(err.message));\n        }\n    };\n    const clearMessages = ()=>{\n        setMessages([\n            {\n                id: \"welcome-message\",\n                role: \"assistant\",\n                content: \"您好，我是您的AI医疗助手。我已经了解了这份评估报告的内容。您可以就报告中的任何内容向我提问，比如手术风险、治疗建议、术后护理等。请问有什么我可以帮助您的吗？\",\n                timestamp: new Date()\n            }\n        ]);\n        setError(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"w-full h-[70vh] flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            \"AI 智能问答\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_RefreshCw_Send_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 27\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: testApiConnection,\n                                        title: \"测试API连接\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_RefreshCw_Send_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: clearMessages,\n                                        title: \"清空对话\",\n                                        children: \"清空\"\n                                    }, void 0, false, {\n                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.Dialog, {\n                                        open: isConfigOpen,\n                                        onOpenChange: setIsConfigOpen,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    title: \"API设置\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_RefreshCw_Send_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogContent, {\n                                                className: \"sm:max-w-[600px]\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogHeader, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogTitle, {\n                                                            children: \"API 配置设置\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid gap-4 py-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                        htmlFor: \"baseUrl\",\n                                                                        children: \"Base URL\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                        lineNumber: 381,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        id: \"baseUrl\",\n                                                                        value: tempConfig.baseUrl,\n                                                                        onChange: (e)=>setTempConfig((prev)=>({\n                                                                                    ...prev,\n                                                                                    baseUrl: e.target.value\n                                                                                })),\n                                                                        placeholder: \"https://api.openai.com/v1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                        lineNumber: 382,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: \"OpenAI API 的基础URL，如使用第三方代理请修改此地址\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                        lineNumber: 388,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                lineNumber: 380,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                        htmlFor: \"apiKey\",\n                                                                        children: \"API Key\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                        lineNumber: 392,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        id: \"apiKey\",\n                                                                        type: \"password\",\n                                                                        value: tempConfig.apiKey,\n                                                                        onChange: (e)=>setTempConfig((prev)=>({\n                                                                                    ...prev,\n                                                                                    apiKey: e.target.value\n                                                                                })),\n                                                                        placeholder: \"sk-...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                        lineNumber: 393,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: \"您的 OpenAI API Key，将安全存储在本地\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                        lineNumber: 400,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                        htmlFor: \"model\",\n                                                                        children: \"模型\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                        lineNumber: 404,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        id: \"model\",\n                                                                        value: tempConfig.model,\n                                                                        onChange: (e)=>setTempConfig((prev)=>({\n                                                                                    ...prev,\n                                                                                    model: e.target.value\n                                                                                })),\n                                                                        placeholder: \"gpt-3.5-turbo\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                        lineNumber: 405,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: \"使用的AI模型，如 gpt-3.5-turbo, gpt-4 等\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                        lineNumber: 411,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-end gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>setIsConfigOpen(false),\n                                                                children: \"取消\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                onClick: handleConfigSave,\n                                                                children: \"保存配置\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                        variant: \"destructive\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_RefreshCw_Send_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.AlertDescription, {\n                                className: \"flex flex-col gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"错误:\"\n                                            }, void 0, false, {\n                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" \",\n                                            error\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs opacity-75\",\n                                        children: \"提示: 请检查 API 配置和网络连接。点击设置按钮配置 API Key 和 Base URL。\"\n                                    }, void 0, false, {\n                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setError(null),\n                                        className: \"w-fit\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_RefreshCw_Send_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-3 w-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"清除错误\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-muted-foreground\",\n                        children: [\n                            \"状态: \",\n                            apiConfig.apiKey ? \"已配置\" : \"未配置\",\n                            \" API Key | 模型: \",\n                            apiConfig.model,\n                            \" | 端点: \",\n                            apiConfig.baseUrl\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                lineNumber: 355,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"flex-1 overflow-hidden\",\n                ref: scrollAreaRef,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                    className: \"h-full pr-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            messages.map((m)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start gap-4 \".concat(m.role === \"user\" ? \"justify-end\" : \"\"),\n                                    children: [\n                                        m.role === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                                            className: \"h-8 w-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                                                children: \"AI\"\n                                            }, void 0, false, {\n                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"rounded-lg p-3 text-sm max-w-[75%] \".concat(m.role === \"user\" ? \"bg-primary text-primary-foreground\" : \"bg-muted\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"whitespace-pre-wrap\",\n                                                    children: m.content\n                                                }, void 0, false, {\n                                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 19\n                                                }, this),\n                                                m.timestamp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs opacity-60 mt-1\",\n                                                    children: m.timestamp.toLocaleTimeString()\n                                                }, void 0, false, {\n                                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 35\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 17\n                                        }, this),\n                                        m.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                                            className: \"h-8 w-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                                                children: \"我\"\n                                            }, void 0, false, {\n                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, m.id, true, {\n                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 15\n                                }, this)),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                                        className: \"h-8 w-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                                            children: \"AI\"\n                                        }, void 0, false, {\n                                            fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-lg p-3 text-sm bg-muted\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_RefreshCw_Send_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin inline mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"正在思考中...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: messagesEndRef\n                            }, void 0, false, {\n                                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                    lineNumber: 450,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                lineNumber: 449,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardFooter, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: onSubmit,\n                    className: \"flex w-full items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                            value: input,\n                            onChange: handleInputChange,\n                            placeholder: \"请就报告内容提问...\",\n                            disabled: isLoading\n                        }, void 0, false, {\n                            fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            type: \"submit\",\n                            disabled: isLoading || !input.trim(),\n                            children: [\n                                isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_RefreshCw_Send_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 26\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_RefreshCw_Send_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 73\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"发送\"\n                                }, void 0, false, {\n                                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                    lineNumber: 491,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n                lineNumber: 490,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/root/zengleilei/workspace/wzu_web_platform/components/report-chat.tsx\",\n        lineNumber: 354,\n        columnNumber: 5\n    }, this);\n}\n_s(ReportChat, \"gk7Crv0HWH1S3OjxYadIieqG/gU=\");\n_c = ReportChat;\nvar _c;\n$RefreshReg$(_c, \"ReportChat\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/report-chat.tsx\n"));

/***/ })

});