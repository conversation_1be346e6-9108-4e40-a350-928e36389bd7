{"version": 3, "file": "Attributes.js", "sourceRoot": "", "sources": ["../../../src/common/Attributes.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Attributes is a map from string to attribute values.\n *\n * Note: only the own enumerable keys are counted as valid attribute keys.\n */\nexport interface Attributes {\n  [attributeKey: string]: AttributeValue | undefined;\n}\n\n/**\n * Attribute values may be any non-nullish primitive value except an object.\n *\n * null or undefined attribute values are invalid and will result in undefined behavior.\n */\nexport type AttributeValue =\n  | string\n  | number\n  | boolean\n  | Array<null | undefined | string>\n  | Array<null | undefined | number>\n  | Array<null | undefined | boolean>;\n"]}