# 温州医科大学 ETOCD 术前评估平台

专业的医学影像分析和术前评估系统，支持NIFTI格式的CT影像分析，提供完整的患者管理和报告功能。

## 🚀 功能特性

### 核心功能
- **CT影像分析** - 支持NIFTI (.nii.gz) 格式文件上传和分析
- **患者管理** - 完整的患者信息管理系统
- **报告管理** - 历史报告查看、筛选和导出
- **AI问答** - 基于OpenAI的智能问答系统

### 技术特性
- **现代化技术栈** - Next.js 15 + React 19 + TypeScript
- **响应式设计** - 支持桌面和移动设备
- **本地数据存储** - 基于文件系统的数据持久化
- **实时更新** - 热重载开发体验

## 📋 系统要求

- Node.js 18.0 或更高版本
- npm 或 yarn 包管理器
- 现代浏览器（Chrome、Firefox、Safari、Edge）

## 🛠️ 安装和运行

### 1. 克隆项目
```bash
git clone <repository-url>
cd wzu_web_platform
```

### 2. 安装依赖
```bash
npm install --legacy-peer-deps
```

### 3. 配置环境变量（可选）
```bash
cp .env.example .env.local
```
编辑 `.env.local` 文件，添加您的OpenAI API密钥：
```
OPENAI_API_KEY=your_openai_api_key_here
```

### 4. 启动开发服务器
```bash
npm run dev
```

### 5. 访问应用
打开浏览器访问 [http://localhost:3000](http://localhost:3000)

## 📖 使用指南

### 主要页面

1. **仪表盘** (`/`) - CT分析工作流程
2. **患者列表** (`/patients`) - 患者信息管理
3. **历史报告** (`/reports`) - 报告查看和管理
4. **系统设置** (`/settings`) - 系统配置

### CT分析流程

1. **选择患者**
   - 搜索现有患者或创建新患者
   - 填写基本信息（姓名、年龄、性别）

2. **上传CT文件**
   - 支持 `.nii.gz` 格式的NIFTI文件
   - 文件大小限制：建议小于100MB

3. **查看分析结果**
   - CT影像三维查看器
   - 详细的评估报告
   - 手术难度评估和建议

4. **AI问答**
   - 点击设置按钮配置OpenAI API
   - 支持自定义API端点和模型
   - 基于报告内容的智能问答
   - 流式响应，实时显示AI回复
   - 完整的对话历史记录
   - API连接测试和错误处理

### AI聊天配置

在使用AI问答功能前，需要配置OpenAI API：

1. **获取API Key**
   - 访问 [OpenAI官网](https://platform.openai.com/api-keys)
   - 创建新的API Key

2. **配置API设置**
   - 点击AI聊天界面右上角的设置按钮
   - 填写API Key、Base URL和模型名称
   - 点击"测试API连接"验证配置
   - 保存配置后即可开始对话

3. **支持的配置**
   - **Base URL**: 默认为OpenAI官方API，支持第三方代理
   - **API Key**: 您的OpenAI API密钥
   - **模型**: 支持gpt-3.5-turbo、gpt-4等模型

### 患者管理

- **添加患者** - 录入完整的患者信息
- **编辑患者** - 修改患者基本信息
- **搜索患者** - 按姓名、电话、身份证搜索
- **查看报告** - 查看患者的所有相关报告

### 报告管理

- **报告列表** - 查看所有评估报告
- **筛选功能** - 按状态、难度、日期筛选
- **报告详情** - 查看完整的报告内容
- **导出功能** - 下载JSON格式的报告数据

## 📁 数据存储

系统使用本地文件存储，数据保存在 `data/` 目录下：

```
data/
├── patients.json          # 患者信息
├── reports.json           # 报告数据
└── uploads/
    └── ct-scans/         # CT文件存储
```

### 数据备份
定期备份 `data/` 目录以防数据丢失：
```bash
cp -r data/ backup/data-$(date +%Y%m%d)
```

## 🔧 开发说明

### 项目结构
```
├── app/                   # Next.js App Router页面
├── components/            # React组件
├── lib/                   # 工具函数和类型定义
├── data/                  # 数据存储目录
└── public/               # 静态资源
```

### 主要技术栈
- **前端框架**: Next.js 15, React 19
- **类型系统**: TypeScript
- **UI组件**: Radix UI + Tailwind CSS
- **状态管理**: React Hooks
- **数据存储**: 本地文件系统
- **AI集成**: OpenAI API

### API接口
- `GET/POST /api/patients` - 患者管理
- `GET/PUT/DELETE /api/patients/[id]` - 单个患者操作
- `GET /api/reports` - 报告列表
- `GET/PUT/DELETE /api/reports/[id]` - 单个报告操作
- `POST /api/analyze-ct` - CT分析
- `POST /api/chat` - AI问答

## 🚨 注意事项

1. **数据安全** - 请定期备份数据目录
2. **文件大小** - CT文件建议压缩后上传
3. **浏览器兼容** - 建议使用最新版本的现代浏览器
4. **API密钥** - 妥善保管OpenAI API密钥，不要提交到版本控制

## 📞 技术支持

如遇到问题，请检查：
1. Node.js版本是否符合要求
2. 依赖是否正确安装
3. 端口3000是否被占用
4. 浏览器控制台是否有错误信息

## 📄 许可证

本项目仅供学术研究和教育使用。
