import { NextResponse } from "next/server"

// 这是一个模拟的API路由。
// 在实际应用中，您需要处理文件上传（例如使用 formidable 或 busboy），
// 然后将文件发送到您的后端分析服务。
export async function POST(request: Request) {
  // 模拟处理延迟
  await new Promise((resolve) => setTimeout(resolve, 2000))

  // 模拟随机生成结果
  const difficulties: Array<"低" | "中" | "高"> = ["低", "中", "高"]
  const randomDifficulty = difficulties[Math.floor(Math.random() * difficulties.length)]
  const randomScore = Math.floor(Math.random() * 100)

  const mockReport = {
    patientInfo: {
      id: `P${Date.now()}`,
      name: "张三",
      age: 58,
      gender: "男",
    },
    analysisResults: {
      difficulty: randomDifficulty,
      score: randomScore,
      keyFindings: ["左侧颞叶发现可疑病变区域。", "血管分布密集，与关键功能区接近。", "初步判断为高级别胶质瘤。"],
      recommendations:
        "建议采用显微神经外科手术，并结合术中神经电生理监测，以最大程度保护神经功能。考虑术前进行功能磁共振成像（fMRI）以精确定位语言和运动中枢。",
    },
    summary: `患者张三，58岁男性，CT影像分析显示左侧颞叶存在可疑病变，血管密集且靠近关键功能区。综合评估手术难度为“${randomDifficulty}”，风险评分为${randomScore}。初步诊断为高级别胶质瘤，建议进行显微手术并辅以术中监测和术前fMRI。`,
  }

  return NextResponse.json(mockReport)
}
