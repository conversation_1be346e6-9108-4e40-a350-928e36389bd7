"use client"

import type React from "react"
import { useState, useRef, useEffect } from "react"
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Send, Loader2, AlertCircle, RefreshCw, Settings } from "lucide-react"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"

interface Message {
  id: string
  role: "user" | "assistant" | "system"
  content: string
  timestamp?: Date
}

interface ReportChatProps {
  reportSummary: string
}

interface ApiConfig {
  baseUrl: string
  apiKey: string
  model: string
}

export function ReportChat({ reportSummary }: ReportChatProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "welcome-message",
      role: "assistant",
      content:
        "您好，我是您的AI医疗助手。我已经了解了这份评估报告的内容。您可以就报告中的任何内容向我提问，比如手术风险、治疗建议、术后护理等。请问有什么我可以帮助您的吗？",
      timestamp: new Date(),
    },
  ])
  const [input, setInput] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [apiConfig, setApiConfig] = useState<ApiConfig>({
    baseUrl: "https://api.openai.com/v1",
    apiKey: "",
    model: "gpt-3.5-turbo",
  })
  const [isConfigOpen, setIsConfigOpen] = useState(false)
  const [tempConfig, setTempConfig] = useState<ApiConfig>(apiConfig)

  // 添加引用来跟踪消息容器和最后一条消息
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // 自动滚动到最新消息
  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" })
    }
  }

  // 当消息列表更新时自动滚动
  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInput(e.target.value)
  }

  const handleConfigSave = () => {
    setApiConfig(tempConfig)
    setIsConfigOpen(false)
    setError(null)
  }

  const sendMessage = async (userMessage: string) => {
    if (!userMessage.trim()) return

    if (!apiConfig.apiKey.trim()) {
      setError("请先在设置中配置 API Key")
      return
    }

    setIsLoading(true)
    setError(null)

    // 添加用户消息
    const newUserMessage: Message = {
      id: `user-${Date.now()}`,
      role: "user",
      content: userMessage,
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, newUserMessage])

    try {
      console.log("发送消息到 API:", userMessage)
      console.log("使用的 API 配置:", {
        baseUrl: apiConfig.baseUrl,
        model: apiConfig.model,
        hasApiKey: !!apiConfig.apiKey,
      })

      // 准备发送给 OpenAI API 的消息
      const apiMessages = [
        {
          role: "system" as const,
          content: `你是温州医科大学ETOCD项目的AI助手。以下是当前患者的术前评估报告摘要，请基于此摘要和你的医学知识库来回答用户的问题。

报告摘要：
"""${reportSummary}"""

请注意：
1. 回答要专业、准确，但要用患者能理解的语言
2. 如果涉及诊断或治疗建议，请提醒患者咨询主治医生
3. 保持温和、耐心的语调
4. 如果问题超出报告范围，请说明并提供一般性建议`,
        },
        ...messages
          .filter((msg) => msg.role !== "system")
          .map((msg) => ({
            role: msg.role as "user" | "assistant",
            content: msg.content,
          })),
        {
          role: "user" as const,
          content: userMessage,
        },
      ]

      console.log("发送到 OpenAI API 的消息数量:", apiMessages.length)

      // 构建 OpenAI API 请求
      const openaiUrl = `${apiConfig.baseUrl.replace(/\/$/, "")}/chat/completions`

      const response = await fetch(openaiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${apiConfig.apiKey}`,
        },
        body: JSON.stringify({
          model: apiConfig.model,
          messages: apiMessages,
          stream: true,
          temperature: 0.7,
          max_tokens: 2000,
        }),
      })

      console.log("OpenAI API 响应状态:", response.status, response.statusText)

      if (!response.ok) {
        let errorMessage = `API请求失败 (${response.status}): ${response.statusText}`
        try {
          const errorData = await response.json()
          console.log("错误响应数据:", errorData)
          if (errorData.error?.message) {
            errorMessage = errorData.error.message
          }
        } catch (e) {
          console.log("无法解析错误响应为 JSON")
          const errorText = await response.text()
          console.log("错误响应文本:", errorText)
          if (errorText) {
            errorMessage = errorText
          }
        }
        throw new Error(errorMessage)
      }
      // 处理流式响应
      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error("无法读取响应流")
      }

      let assistantMessage = ""
      const assistantMessageId = `assistant-${Date.now()}`

      // 添加空的助手消息
      setMessages((prev) => [
        ...prev,
        {
          id: assistantMessageId,
          role: "assistant",
          content: "",
          timestamp: new Date(),
        },
      ])

      const decoder = new TextDecoder()

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value, { stream: true })
          console.log("收到数据块:", chunk)

          // 解析 OpenAI 流式响应
          const lines = chunk.split("\n")

          for (const line of lines) {
            const trimmedLine = line.trim()

            if (trimmedLine === "") continue
            if (trimmedLine === "data: [DONE]") {
              console.log("流式响应完成")
              break
            }

            if (trimmedLine.startsWith("data: ")) {
              const jsonStr = trimmedLine.slice(6)

              try {
                const data = JSON.parse(jsonStr)

                if (data.choices && data.choices[0] && data.choices[0].delta) {
                  const delta = data.choices[0].delta

                  if (delta.content) {
                    assistantMessage += delta.content

                    // 更新消息
                    setMessages((prev) =>
                      prev.map((msg) => (msg.id === assistantMessageId ? { ...msg, content: assistantMessage } : msg)),
                    )
                  }
                }
              } catch (parseError) {
                console.log("解析流式数据失败:", parseError, "数据:", jsonStr)
              }
            }
          }
        }

        // 如果没有收到任何内容，显示错误
        if (!assistantMessage.trim()) {
          throw new Error("AI 服务没有返回有效响应")
        }

        console.log("完整的助手回复:", assistantMessage)
      } finally {
        reader.releaseLock()
      }
    } catch (err: any) {
      console.error("发送消息时出错:", err)

      let errorMessage = err.message || "发送消息时出现未知错误"

      // 处理常见错误
      if (errorMessage.includes("401")) {
        errorMessage = "API Key 无效，请检查配置"
      } else if (errorMessage.includes("429")) {
        errorMessage = "请求过于频繁，请稍后再试"
      } else if (errorMessage.includes("404")) {
        errorMessage = "API 端点不存在，请检查 Base URL 配置"
      } else if (errorMessage.includes("NetworkError") || errorMessage.includes("Failed to fetch")) {
        errorMessage = "网络连接失败，请检查网络或 Base URL 是否正确"
      }

      setError(errorMessage)

      // 移除用户消息（如果发送失败）
      setMessages((prev) => prev.filter((msg) => msg.id !== newUserMessage.id))
    } finally {
      setIsLoading(false)
    }
  }

  const testApiConnection = async () => {
    if (!apiConfig.apiKey.trim()) {
      setError("请先配置 API Key")
      return
    }

    setError(null)

    try {
      console.log("测试 OpenAI API 连接...")

      const openaiUrl = `${apiConfig.baseUrl.replace(/\/$/, "")}/chat/completions`

      const response = await fetch(openaiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${apiConfig.apiKey}`,
        },
        body: JSON.stringify({
          model: apiConfig.model,
          messages: [
            {
              role: "user",
              content: "你好，这是一个连接测试，请简单回复确认",
            },
          ],
          max_tokens: 50,
        }),
      })

      console.log("测试响应状态:", response.status)

      if (!response.ok) {
        const errorData = await response.json()
        console.error("测试响应错误:", errorData)
        throw new Error(errorData.error?.message || `HTTP ${response.status}`)
      } else {
        const data = await response.json()
        console.log("API 连接测试成功:", data)
        setError(null)

        // 显示成功消息
        setMessages((prev) => [
          ...prev,
          {
            id: `test-${Date.now()}`,
            role: "assistant",
            content: "✅ API 连接测试成功！配置正确。",
            timestamp: new Date(),
          },
        ])
      }
    } catch (err: any) {
      console.error("API 连接测试失败:", err)
      setError(`API 连接测试失败: ${err.message}`)
    }
  }

  const clearMessages = () => {
    setMessages([
      {
        id: "welcome-message",
        role: "assistant",
        content:
          "您好，我是您的AI医疗助手。我已经了解了这份评估报告的内容。您可以就报告中的任何内容向我提问，比如手术风险、治疗建议、术后护理等。请问有什么我可以帮助您的吗？",
        timestamp: new Date(),
      },
    ])
    setError(null)
  }

  return (
    <Card className="w-full h-[70vh] flex flex-col">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          AI 智能问答
          <div className="flex items-center gap-2">
            {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}

            <Button variant="ghost" size="sm" onClick={testApiConnection} title="测试API连接">
              <RefreshCw className="h-4 w-4" />
            </Button>

            <Button variant="ghost" size="sm" onClick={clearMessages} title="清空对话">
              清空
            </Button>

            <Dialog open={isConfigOpen} onOpenChange={setIsConfigOpen}>
              <DialogTrigger asChild>
                <Button variant="ghost" size="sm" title="API设置">
                  <Settings className="h-4 w-4" />
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>API 配置设置</DialogTitle>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid gap-2">
                    <Label htmlFor="baseUrl">Base URL</Label>
                    <Input
                      id="baseUrl"
                      value={tempConfig.baseUrl}
                      onChange={(e) => setTempConfig((prev) => ({ ...prev, baseUrl: e.target.value }))}
                      placeholder="https://api.openai.com/v1"
                    />
                    <p className="text-xs text-muted-foreground">OpenAI API 的基础URL，如使用第三方代理请修改此地址</p>
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="apiKey">API Key</Label>
                    <Input
                      id="apiKey"
                      type="password"
                      value={tempConfig.apiKey}
                      onChange={(e) => setTempConfig((prev) => ({ ...prev, apiKey: e.target.value }))}
                      placeholder="sk-..."
                    />
                    <p className="text-xs text-muted-foreground">您的 OpenAI API Key，将安全存储在本地</p>
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="model">模型</Label>
                    <Input
                      id="model"
                      value={tempConfig.model}
                      onChange={(e) => setTempConfig((prev) => ({ ...prev, model: e.target.value }))}
                      placeholder="gpt-3.5-turbo"
                    />
                    <p className="text-xs text-muted-foreground">使用的AI模型，如 gpt-3.5-turbo, gpt-4 等</p>
                  </div>
                </div>

                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsConfigOpen(false)}>
                    取消
                  </Button>
                  <Button onClick={handleConfigSave}>保存配置</Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardTitle>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="flex flex-col gap-2">
              <div>
                <strong>错误:</strong> {error}
              </div>
              <div className="text-xs opacity-75">
                提示: 请检查 API 配置和网络连接。点击设置按钮配置 API Key 和 Base URL。
              </div>
              <Button variant="outline" size="sm" onClick={() => setError(null)} className="w-fit">
                <RefreshCw className="h-3 w-3 mr-1" />
                清除错误
              </Button>
            </AlertDescription>
          </Alert>
        )}

        <div className="text-xs text-muted-foreground">
          状态: {apiConfig.apiKey ? "已配置" : "未配置"} API Key | 模型: {apiConfig.model} | 端点: {apiConfig.baseUrl}
        </div>
      </CardHeader>

      <CardContent className="flex-1 overflow-hidden" ref={scrollAreaRef}>
        <ScrollArea className="h-full pr-4">
          <div className="space-y-4">
            {messages.map((m) => (
              <div key={m.id} className={`flex items-start gap-4 ${m.role === "user" ? "justify-end" : ""}`}>
                {m.role === "assistant" && (
                  <Avatar className="h-8 w-8">
                    <AvatarFallback>AI</AvatarFallback>
                  </Avatar>
                )}
                <div
                  className={`rounded-lg p-3 text-sm max-w-[75%] ${
                    m.role === "user" ? "bg-primary text-primary-foreground" : "bg-muted"
                  }`}
                >
                  <div className="whitespace-pre-wrap">{m.content}</div>
                  {m.timestamp && <div className="text-xs opacity-60 mt-1">{m.timestamp.toLocaleTimeString()}</div>}
                </div>
                {m.role === "user" && (
                  <Avatar className="h-8 w-8">
                    <AvatarFallback>我</AvatarFallback>
                  </Avatar>
                )}
              </div>
            ))}
            {isLoading && (
              <div className="flex items-start gap-4">
                <Avatar className="h-8 w-8">
                  <AvatarFallback>AI</AvatarFallback>
                </Avatar>
                <div className="rounded-lg p-3 text-sm bg-muted">
                  <Loader2 className="h-4 w-4 animate-spin inline mr-2" />
                  正在思考中...
                </div>
              </div>
            )}
            {/* 添加一个空的div作为滚动目标 */}
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>
      </CardContent>

      <CardFooter>
        <div className="flex w-full items-center space-x-2">
          <Input
            value={input}
            onChange={handleInputChange}
            placeholder={apiConfig.apiKey ? "请就报告内容提问..." : "请先配置 API Key"}
            disabled={isLoading || !apiConfig.apiKey}
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault()
                if (!isLoading && input.trim() && apiConfig.apiKey) {
                  const userMessage = input.trim()
                  setInput("")
                  sendMessage(userMessage)
                }
              }
            }}
          />
          <Button
            onClick={() => {
              if (!isLoading && input.trim() && apiConfig.apiKey) {
                const userMessage = input.trim()
                setInput("")
                sendMessage(userMessage)
              }
            }}
            disabled={isLoading || !input.trim() || !apiConfig.apiKey}
          >
            {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Send className="h-4 w-4" />}
            <span className="sr-only">发送</span>
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
}
