"use client"

import type React from "react"

import { useChat } from "ai/react"
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Send, Loader2 } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useEffect } from "react"

interface ReportChatProps {
  reportSummary: string
}

export function ReportChat({ reportSummary }: ReportChatProps) {
  const { messages, input, handleInputChange, handleSubmit, isLoading, error } = useChat({
    api: "/api/chat", // 明确指定 API 路径
    initialMessages: [
      {
        id: "system-message",
        role: "system",
        content: `你是温州医科大学ETOCD项目的AI助手。以下是当前患者的术前评估报告摘要，请基于此摘要和你的医学知识库来回答用户的问题。摘要："""${reportSummary}"""`,
      },
      {
        id: "welcome-message",
        role: "assistant",
        content:
          "您好，我是您的AI医疗助手。我已经了解了这份评估报告的内容。您可以就报告中的任何内容向我提问，比如手术风险、治疗建议、术后护理等。请问有什么我可以帮助您的吗？",
      },
    ],
    onError: (error) => {
      console.error("Chat error:", error)
    },
    onFinish: (message) => {
      console.log("Message finished:", message)
    },
  })

  // 调试信息
  useEffect(() => {
    console.log("Chat messages:", messages)
    console.log("Chat isLoading:", isLoading)
    console.log("Chat error:", error)
  }, [messages, isLoading, error])

  const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (!input.trim()) return

    console.log("Submitting message:", input)
    handleSubmit(e)
  }

  return (
    <Card className="w-full h-[70vh] flex flex-col">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          AI 智能问答
          {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
        </CardTitle>
        {error && <div className="text-sm text-red-600 bg-red-50 p-2 rounded">错误: {error.message}</div>}
      </CardHeader>
      <CardContent className="flex-1 overflow-hidden">
        <ScrollArea className="h-full pr-4">
          <div className="space-y-4">
            {messages
              .filter((m) => m.role !== "system")
              .map((m) => (
                <div key={m.id} className={`flex items-start gap-4 ${m.role === "user" ? "justify-end" : ""}`}>
                  {m.role === "assistant" && (
                    <Avatar className="h-8 w-8">
                      <AvatarImage src="/placeholder.svg?width=32&height=32" />
                      <AvatarFallback>AI</AvatarFallback>
                    </Avatar>
                  )}
                  <div
                    className={`rounded-lg p-3 text-sm max-w-[75%] ${
                      m.role === "user" ? "bg-primary text-primary-foreground" : "bg-muted"
                    }`}
                  >
                    {m.content}
                  </div>
                  {m.role === "user" && (
                    <Avatar className="h-8 w-8">
                      <AvatarImage src="/placeholder.svg?width=32&height=32" />
                      <AvatarFallback>ME</AvatarFallback>
                    </Avatar>
                  )}
                </div>
              ))}
            {isLoading && (
              <div className="flex items-start gap-4">
                <Avatar className="h-8 w-8">
                  <AvatarImage src="/placeholder.svg?width=32&height=32" />
                  <AvatarFallback>AI</AvatarFallback>
                </Avatar>
                <div className="rounded-lg p-3 text-sm bg-muted">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  正在思考中...
                </div>
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>
      <CardFooter>
        <form onSubmit={onSubmit} className="flex w-full items-center space-x-2">
          <Input value={input} onChange={handleInputChange} placeholder="请就报告内容提问..." disabled={isLoading} />
          <Button type="submit" disabled={isLoading || !input.trim()}>
            {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Send className="h-4 w-4" />}
            <span className="sr-only">发送</span>
          </Button>
        </form>
      </CardFooter>
    </Card>
  )
}
