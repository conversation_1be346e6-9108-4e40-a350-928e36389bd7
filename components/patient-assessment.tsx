"use client"

import * as React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { CtViewer } from "@/components/ct-viewer"
import { AssessmentReport } from "@/components/assessment-report"
import { ReportChat } from "@/components/report-chat"
import { Loader2, Upload } from "lucide-react"

// 定义API返回的报告数据类型
interface ReportData {
  patientInfo: {
    id: string
    name: string
    age: number
    gender: string
  }
  analysisResults: {
    difficulty: "低" | "中" | "高"
    score: number
    keyFindings: string[]
    recommendations: string
  }
  summary: string
}

export function PatientAssessment() {
  const [activeTab, setActiveTab] = React.useState("upload")
  const [isLoading, setIsLoading] = React.useState(false)
  const [ctFile, setCtFile] = React.useState<File | null>(null)
  const [reportData, setReportData] = React.useState<ReportData | null>(null)
  const fileInputRef = React.useRef<HTMLInputElement>(null)
  const [error, setError] = React.useState<string | null>(null) // Added error state

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0]
      if (file.name.endsWith(".nii.gz")) {
        setCtFile(file)
        setError(null) // Clear previous errors on new file selection
      } else {
        setCtFile(null) // Clear invalid file
        setError("请上传 .nii.gz 格式的文件")
        // alert("请上传 .nii.gz 格式的文件") // Alert can be redundant if error is displayed
      }
    }
  }

  const handleAnalyze = async () => {
    if (!ctFile) {
      setError("请先选择一个CT文件")
      // alert("请先选择一个CT文件")
      return
    }
    setIsLoading(true)
    setReportData(null)
    setError(null) // Clear previous errors before new analysis

    // 在实际应用中，你会使用 FormData 来上传文件
    // const formData = new FormData();
    // formData.append('file', ctFile);

    try {
      const response = await fetch("/api/analyze-ct", {
        method: "POST",
        headers: {
          "Content-Type": "application/json", // Specify content type
        },
        // Send a minimal JSON body. For a real file upload, you'd use:
        // body: formData,
        // (and remove the Content-Type header above, fetch sets it for FormData)
        body: JSON.stringify({ fileName: ctFile.name }),
      })

      if (!response.ok) {
        let errorDetails = `HTTP error! Status: ${response.status}`
        try {
          // Try to get more specific error message from API response body
          const errorData = await response.json()
          errorDetails += ` - ${errorData.message || JSON.stringify(errorData)}`
        } catch (e) {
          // If response is not JSON or parsing fails, use text
          const textError = await response.text()
          if (textError) {
            errorDetails += ` - ${textError}`
          }
        }
        console.error("API call failed:", errorDetails)
        throw new Error(`分析失败: ${errorDetails}`)
      }

      const data: ReportData = await response.json()
      setReportData(data)
      setActiveTab("report")
    } catch (err: any) {
      // Catch any error, including the one thrown above
      console.error("分析过程中出现错误:", err)
      const displayError = err.message || "分析过程中出现未知错误，请检查控制台或重试。"
      setError(displayError)
      // alert(displayError); // Alert can be redundant
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="upload">1. 上传数据</TabsTrigger>
        <TabsTrigger value="report" disabled={!reportData}>
          2. 查看报告
        </TabsTrigger>
        <TabsTrigger value="chat" disabled={!reportData}>
          3. AI 问答
        </TabsTrigger>
      </TabsList>
      <TabsContent value="upload">
        <Card>
          <CardHeader>
            <CardTitle>上传 CT 数据</CardTitle>
            <CardDescription>请上传 `.nii.gz` 格式的CT影像文件以进行手术前评估。</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid w-full max-w-sm items-center gap-1.5">
              <Label htmlFor="ct-file">CT 文件</Label>
              <Input id="ct-file" type="file" accept=".nii.gz" onChange={handleFileChange} ref={fileInputRef} />
            </div>
            {ctFile && <p className="text-sm text-muted-foreground">已选择文件: {ctFile.name}</p>}
            {error && <p className="text-sm font-semibold text-red-600">{error}</p>} {/* Display error message */}
            <Button onClick={handleAnalyze} disabled={!ctFile || isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  分析中...
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  开始分析
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="report">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
          <Card className="lg:col-span-4">
            <CardHeader>
              <CardTitle>CT 影像查看器</CardTitle>
            </CardHeader>
            <CardContent>{ctFile && <CtViewer file={ctFile} />}</CardContent>
          </Card>
          <div className="lg:col-span-3">{reportData && <AssessmentReport data={reportData} />}</div>
        </div>
      </TabsContent>
      <TabsContent value="chat">{reportData && <ReportChat reportSummary={reportData.summary} />}</TabsContent>
    </Tabs>
  )
}
